import request from '@/utils/request'

// PDF文件上传
export function EquipUploadPdf(data) {
  return request({
    url: '/EquipmentUpload/EquipUploadPdf',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除PDF文件
export function DeleteEquipPdf(fileID) {
  return request({
    url: '/EquipmentUpload/DeleteEquipPdf?fileID=' + fileID,
    method: 'get',
  })
}
