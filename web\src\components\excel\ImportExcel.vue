<!--
 * FilePath     : \src\components\excel\ImportExcel.vue
 * Author       : 苏军志
 * Date         : 2022-04-17 16:45
 * LastEditors  : 苏军志
 * LastEditTime : 2022-04-21 20:31
 * Description  : 导入Excel
 * CodeIterationRecord: 
-->
<template>
  <el-upload
    class="import-excel"
    action=""
    accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    :auto-upload="false"
    :show-file-list="false"
    :on-change="getExcelFile"
  >
    <div slot="trigger">
      <slot name="trigger">
        <el-button type="warning" size="mini" icon="el-icon-upload"
          >导入Excel</el-button
        >
      </slot>
    </div>
  </el-upload>
</template>

<script>
// 不需要--因为xlsx是个插件--直接require就行了--无需注入Vue
// import Vue from "vue";
// import XLSX from "xlsx";
// Vue.use(XLSX);
import "@/utils/excel/Blob.js";
import "@/utils/excel/Export2Excel.js";
export default {
  props: {
    // Excel列名和字段名对应关系
    columnMap: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    getExcelFile(selectFile) {
      var rABS = false; //是否将文件读取为二进制字符串
      var file = selectFile.raw;
      var reader = new FileReader();
      FileReader.prototype.readAsBinaryString = (file) => {
        var binary = "";
        var rABS = false; //是否将文件读取为二进制字符串
        var pt = this;
        var wb; //读取完成的数据
        var outdata;
        var reader = new FileReader();
        reader.onload = (e) => {
          var bytes = new Uint8Array(reader.result);
          var length = bytes.byteLength;
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          var XLSX = require("xlsx");
          if (rABS) {
            wb = XLSX.read(btoa(fixdata(binary)), {
              //手动转化
              type: "base64",
              //设为true，将天数的时间戳转为时间格式 Sat Mar 15 2022 08:00:00 GMT+0800 (中国标准时间)
              cellDates: true,
            });
          } else {
            wb = XLSX.read(binary, {
              type: "binary",
              //设为true，将天数的时间戳转为时间格式 Sat Mar 15 2022 08:00:00 GMT+0800 (中国标准时间)
              cellDates: true,
            });
          }
          let outData = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
          // 获取Excel文件内容
          if (outData && outData.length > 0) {
            outData.forEach((row) => {
              for (let key in row) {
                // 如果是日期类型，则转换日期格式
                if (/GMT\+0800 \(中国标准时间\)/.test(row[key])) {
                  let date = this._datetimeUtil.formatDate(row[key]);
                  // xlsx将excel中的时间内容解析后，会小少43秒，所以需要加上43秒
                  row[key] = this._datetimeUtil.addSeconds(date, 43);
                }
                if (this.columnMap && this.columnMap.length > 0) {
                  let column = this.columnMap.find(
                    (item) => item.label === key
                  );
                  if (column) {
                    row[column.key] = row[key];
                    delete row[key];
                  }
                }
              }
            });
          }
          this.$emit("resultData", outData);
        };
        reader.readAsArrayBuffer(file);
      };
      if (rABS) {
        reader.readAsArrayBuffer(file);
      } else {
        reader.readAsBinaryString(file);
      }
    },
  },
};
</script>

<style>
.import-excel {
  display: inline-block;
}
</style>