import request from '@/utils/request'
const baseURL = "config/";

// 查询参数列表
export function listConfig(query) {
  return request({
    url: baseURL + "listConfig",
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getConfig(configId) {
  return request({
    url: baseURL + "getConfig?configId=" + configId,
    method: 'get'
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  return request({
    url: baseURL + "getConfigKey?configKey=" + configKey,
    method: 'get'
  })
}

// 新增参数配置
export function addConfig(data) {
  return request({
    url: baseURL + "addConfig",
    method: 'post',
    data: data
  })
}

// 修改参数配置
export function updateConfig(data) {
  return request({
    url:  baseURL + "updateConfig",
    method: 'put',
    data: data
  })
}

// 删除参数配置
export function delConfig(configId) {
  return request({
    url: baseURL + "delConfig?configId=" + configId,
    method: 'delete'
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: baseURL + "refreshCache",
    method: 'delete'
  })
}
