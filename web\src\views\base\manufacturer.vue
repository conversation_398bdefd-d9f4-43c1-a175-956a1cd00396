<template>
  <div class="producer-management app-container">
    <!-- 标题和操作按钮 -->
  
  
    <!-- 搜索区域 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="生产厂商代码">
        <el-input v-model="searchForm.code" placeholder="请输入代码"></el-input>
      </el-form-item>
      <el-form-item label="生产厂商名称">
        <el-input v-model="searchForm.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="primary" @click="handleAdd">新增生产商</el-button>
      </el-form-item>
    </el-form>
  
    <!-- 生产商列表 -->
    <el-table :data="producerList" border stripe style="width: 100%; margin-top: 10px">
      <el-table-column prop="code" label="生产厂商代码"></el-table-column>
      <el-table-column prop="name" label="生产厂商名称"></el-table-column>
      <el-table-column prop="pinyinCode" label="拼音码"></el-table-column>
      <el-table-column prop="contactPerson" label="联系人"></el-table-column>
      <el-table-column prop="contactPhone" label="联系电话"></el-table-column>
      <el-table-column prop="mobile" label="手机"></el-table-column>
      <el-table-column prop="developDate" label="发展日期"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="scope.row.stopDate ? 'danger' : 'success'">
            {{ scope.row.stopDate ? '已停用' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  
    <!-- 分页 -->
    <div class="pagination" v-if="total > 0">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="total"></el-pagination>
    </div>
  
    <!-- 生产商表单弹窗 -->
    <el-dialog :title="isEdit ? '编辑生产商' : '新增生产商'" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="producer-form">
        <el-row :gutter="20">
          <!-- 左侧字段 -->
          <el-col :span="12">
            <el-form-item label="生产厂商代码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入生产厂商代码" :disabled="isEdit" />
            </el-form-item>
            <el-form-item label="拼音码" prop="pinyinCode">
              <el-input v-model="formData.pinyinCode" placeholder="请输入拼音码" />
            </el-form-item>
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
            <el-form-item label="手机" prop="mobile">
              <el-input v-model="formData.mobile" placeholder="请输入手机号" />
            </el-form-item>
            <el-form-item label="传真" prop="fax">
              <el-input v-model="formData.fax" placeholder="请输入传真" />
            </el-form-item>
            <el-form-item label="法人代表" prop="legalRepresentative">
              <el-input v-model="formData.legalRepresentative" placeholder="请输入法人代表" />
            </el-form-item>
            <el-form-item label="Email" prop="email">
              <el-input v-model="formData.email" placeholder="请输入Email" />
            </el-form-item>
            <el-form-item label="企业网址" prop="enterpriseUrl">
              <el-input v-model="formData.enterpriseUrl" placeholder="请输入企业网址" />
            </el-form-item>
          </el-col>
  
          <!-- 右侧字段 -->
          <el-col :span="12">
            <el-form-item label="生产厂商名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入生产厂商名称" />
            </el-form-item>
            <el-form-item label="厂商地址" prop="address">
              <el-input v-model="formData.address" placeholder="请输入厂商地址" />
            </el-form-item>
            <el-form-item label="发展日期" prop="developDate">
              <el-date-picker v-model="formData.developDate" type="date" value-format="YYYY-MM-DD"
                placeholder="请选择发展日期" />
            </el-form-item>
            <el-form-item label="停用日期" prop="stopDate">
              <el-date-picker v-model="formData.stopDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择停用日期" />
            </el-form-item>
            <el-form-item label="是否物流管理" prop="logisticsManagement">
              <el-checkbox v-model="formData.logisticsManagement">是</el-checkbox>
            </el-form-item>
            <el-form-item label="是否固定资产" prop="fixedAsset">
              <el-checkbox v-model="formData.fixedAsset">是</el-checkbox>
            </el-form-item>
            <el-form-item label="是否无形资产" prop="intangibleAsset">
              <el-checkbox v-model="formData.intangibleAsset">是</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
  
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { SelectManufacturer, AddManufacturer, DeletManufacturer } from '@/api/base/manufacturer';
export default {
  name: 'manufacturer',
  data() {
    return {
      // 搜索表单
      searchForm: {
        code: '',
        name: ''
      },
      // 生产商列表数据
      producerList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,

      // 表单弹窗相关
      dialogVisible: false,
      isEdit: false,
      formData: {
        code: '',
        name: '',
        pinyinCode: '',
        contactPhone: '',
        contactPerson: '',
        mobile: '',
        fax: '',
        legalRepresentative: '',
        email: '',
        enterpriseUrl: '',
        address: '',
        developDate: '',
        stopDate: '',
        logisticsManagement: false,
        fixedAsset: false,
        intangibleAsset: false
      },
      // 表单验证规则
      rules: {
        code: [
          { required: true, message: '请输入生产厂商代码', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入生产厂商名称', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] }
        ],
        contactPhone: [
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
        ],
        mobile: [
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
        ],
        enterpriseUrl: [
          { type: 'url', message: '请输入正确的网址格式', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  created() {
    // 初始化加载数据
    this.loadProducerList();
  },
  methods: {
    // 加载生产商列表
    loadProducerList() {
      // 合并分页参数和搜索参数
      const params = {
        page: this.currentPage,
        size: this.pageSize,
        ...this.searchForm
      };

      // 模拟API请求
      console.log('加载生产商列表参数:', params);

      // 模拟返回数据
      this.producerList = [
        {
          code: 'PD001',
          name: '北京电子科技有限公司',
          pinyinCode: 'BJDZ',
          contactPhone: '010-12345678',
          contactPerson: '张三',
          mobile: '13800138000',
          fax: '010-87654321',
          legalRepresentative: '李四',
          email: '<EMAIL>',
          enterpriseUrl: 'http://www.bjdzyx.com',
          address: '北京市海淀区科技园区100号',
          developDate: '2010-05-15',
          stopDate: '',
          logisticsManagement: true,
          fixedAsset: true,
          intangibleAsset: false
        },
        {
          code: 'PD002',
          name: '上海机械制造集团',
          pinyinCode: 'SHJX',
          contactPhone: '021-12345678',
          contactPerson: '王五',
          mobile: '13900139000',
          fax: '021-87654321',
          legalRepresentative: '赵六',
          email: '<EMAIL>',
          enterpriseUrl: 'http://www.shjxz.com',
          address: '上海市浦东新区工业区200号',
          developDate: '2005-08-20',
          stopDate: '',
          logisticsManagement: false,
          fixedAsset: true,
          intangibleAsset: false
        }
      ];
      this.total = 2; // 模拟总条数
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1; // 重置页码
      this.loadProducerList();
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        code: '',
        name: ''
      };
      this.currentPage = 1;
      this.loadProducerList();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.loadProducerList();
    },

    // 当前页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadProducerList();
    },

    // 新增生产商
    handleAdd() {
      this.isEdit = false;
      this.formData = {
        code: '',
        name: '',
        pinyinCode: '',
        contactPhone: '',
        contactPerson: '',
        mobile: '',
        fax: '',
        legalRepresentative: '',
        email: '',
        enterpriseUrl: '',
        address: '',
        developDate: '',
        stopDate: '',
        logisticsManagement: false,
        fixedAsset: false,
        intangibleAsset: false
      };
      this.dialogVisible = true;
    },

    // 编辑生产商
    handleEdit(row) {
      this.isEdit = true;
      // 深拷贝，避免直接修改表格数据
      this.formData = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },

    // 删除生产商
    handleDelete(row) {
      this.$confirm(`确定要删除生产商【${row.name}】吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟API请求
        console.log('删除生产商:', row.code);

        // 模拟删除成功
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.loadProducerList();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    // 提交表单
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 表单验证通过，提交数据
          if (this.isEdit) {
            // 编辑操作
            console.log('更新生产商:', this.formData);

            // 模拟API请求成功
            this.$message({
              type: 'success',
              message: '更新成功!'
            });
          } else {
            // 新增操作
            console.log('新增生产商:', this.formData);

            // 模拟API请求成功
            this.$message({
              type: 'success',
              message: '新增成功!'
            });
          }
          this.dialogVisible = false;
          this.loadProducerList();
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.header-operation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.search-form {}

.pagination {
  margin-top: 15px;
  text-align: right;
}

::v-deep .producer-form {
  margin-top: 10px;
}

::v-deep .el-row {
  margin-bottom: 10px;
}
</style>
