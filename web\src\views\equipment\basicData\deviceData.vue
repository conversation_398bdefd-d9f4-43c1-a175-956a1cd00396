<template>
    <!-- 设别信息维护主页面 -->
    <div class="device-management-page">
        <!-- 页面标题栏 -->
        <div class="page-header">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="el-icon-setting"></i>
                    医用耗材信息维护
                </h1>
                <span class="page-subtitle">医用耗材设备信息的统一管理和维护</span>
            </div>
            <div class="header-right">
                <el-button type="text" @click="showSearch = !showSearch" class="toggle-search-btn">
                    <i :class="showSearch ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
                    {{ showSearch ? '收起筛选' : '展开筛选' }}
                </el-button>
            </div>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section" v-show="showSearch">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
                <el-form-item label="条形码号">
                    <el-input v-model="queryParams.barcode" placeholder="条形码号" clearable size="mini"
                        style="width: 120px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="院内编码">
                    <el-input v-model="queryParams.hospitalCode" placeholder="院内编码" clearable size="mini"
                        style="width: 120px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="耗材名称">
                    <el-input v-model="queryParams.materialName" placeholder="耗材名称" clearable size="mini"
                        style="width: 140px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="厂家">
                    <el-input v-model="queryParams.manufacturer" placeholder="厂家" clearable size="mini"
                        style="width: 120px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="物资分类">
                    <el-select v-model="queryParams.materialCategory" placeholder="物资分类" clearable size="mini"
                        style="width: 120px">
                        <el-option v-for="dict in dict.type.material_category" :key="dict.value" :label="dict.label"
                            :value="dict.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否高值">
                    <el-select v-model="queryParams.isHighValue" placeholder="是否高值" clearable size="mini"
                        style="width: 100px">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 按钮 -->
        <div class="toolbar-section">
            <div class="toolbar-left">
                <el-button type="primary" icon="el-icon-plus" @click="handleAdd" v-hasPermi="['equipment:device:add']">
                    新增设备
                </el-button>
                <el-button type="danger" icon="el-icon-delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['equipment:device:remove']">
                    批量删除
                </el-button>
                <el-button type="info" icon="el-icon-document" @click="downloadTemplate" plain>
                    下载模板
                </el-button>
                <el-button type="success" icon="el-icon-upload2" @click="handleImport"
                    v-hasPermi="['equipment:device:import']">
                    导入数据
                </el-button>
                <el-button type="warning" icon="el-icon-download" @click="handleExport"
                    v-hasPermi="['equipment:device:export']">
                    导出数据
                </el-button>
            </div>
        </div>

        <!-- 隐藏的文件输入框 -->
        <input ref="fileInput" type="file" accept=".xlsx,.xls" @change="handleFileChange" style="display: none;" />

        <!-- 表格 -->
        <div class="table-container">
            <div class="table-header">
                <div class="table-title">
                    <i class="el-icon-menu"></i>
                    <span>设备信息列表</span>
                </div>
            </div>
            <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange"
                class="main-table" stripe border size="mini">
                <el-table-column label="操作" align="center" width="100">
                    <template slot-scope="scope">
                        <el-dropdown trigger="click" @command="handleCommand" size="small">
                            <el-button size="mini" type="primary" plain>
                                操作<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="{ action: 'detail', row: scope.row }" icon="el-icon-view">
                                    查看详情
                                </el-dropdown-item>
                                <el-dropdown-item :command="{ action: 'edit', row: scope.row }" icon="el-icon-edit"
                                    v-hasPermi="['equipment:device:edit']">
                                    修改信息
                                </el-dropdown-item>
                                <el-dropdown-item :command="{ action: 'upload', row: scope.row }"
                                    icon="el-icon-folder-opened" v-hasPermi="['equipment:device:upload']">
                                    证件管理
                                </el-dropdown-item>
                                <el-dropdown-item :command="{ action: 'delete', row: scope.row }" icon="el-icon-delete"
                                    v-hasPermi="['equipment:device:remove']" divided>
                                    删除
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-table-column>
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="条形码号" align="center" prop="barcode" width="130">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.barcode" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="院内编码" align="center" prop="hospitalCode" width="120">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.hospitalCode" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="27位医保码" align="center" prop="medicalInsuranceCode" width="150">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.medicalInsuranceCode" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="物资分类" align="center" prop="materialCategory" width="100">
                    <template slot-scope="scope">
                        <expandable-text :text="getDictLabel('material_category', scope.row.materialCategory)"
                            :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="医用耗材级别" align="center" prop="materialLevel" width="120">
                    <template slot-scope="scope">
                        <expandable-text :text="getDictLabel('material_level', scope.row.materialLevel)"
                            :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="风险等级" align="center" prop="riskLevel" width="100">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.risk_level" :value="scope.row.riskLevel" />
                    </template>
                </el-table-column>
                <el-table-column label="耗材名称" align="left" prop="materialName" width="150">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.materialName" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="通用名" align="left" prop="genericName" width="120">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.genericName" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="规格" align="center" prop="specification" width="100">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.specification" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="型号" align="center" prop="model" width="100">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.modelxh" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="单位" align="center" prop="unit" width="80" />
                <el-table-column label="注册证号" align="center" prop="registrationNumber" width="140">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.registrationNumber" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="厂家" align="left" prop="manufacturer" width="120">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.manufacturer" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="批号" align="center" prop="batchNumber" width="100" />
                <el-table-column label="是否高值" align="center" prop="isHighValue" width="90">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.yes_no" :value="scope.row.isHighValue" />
                    </template>
                </el-table-column>
                <el-table-column label="是否可收费" align="center" prop="isChargeable" width="100">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.yes_no" :value="scope.row.isChargeable" />
                    </template>
                </el-table-column>
                <el-table-column label="入库价格" align="center" prop="purchasePrice" width="110">
                    <template slot-scope="scope">
                        <span class="price-text">{{ scope.row.purchasePrice ? '￥' + scope.row.purchasePrice : '-'
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="售价" align="center" prop="salePrice" width="110">
                    <template slot-scope="scope">
                        <span class="price-text">{{ scope.row.salePrice ? '￥' + scope.row.salePrice : '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="供应商" align="left" prop="supplier" min-width="120">
                    <template slot-scope="scope">
                        <expandable-text :text="scope.row.supplier" :max-length="6" />
                    </template>
                </el-table-column>
                <el-table-column label="有效期" align="center" prop="expiryDate" width="110">
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.expiryDate, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-wrapper">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="total" />
        </div>

        <!-- 设备信息维护弹框 -->
        <operate-dia ref="operateDia" @refresh="getList" />

        <!-- pdf文件上传弹框 -->
        <pdf-upload ref="pdfUpload" @refresh="getList" @refresh-upload-status="handleRefreshUploadStatus" />
    </div>
</template>

<script>
import OperateDia from './components/operateDia'
import PdfUpload from './components/pdfUpload'
import { listDevice, delDevice, importDevice } from "@/api/equipment/device";
import { getUserProfile } from '@/api/system/user';

const ExpandableText = {
    props: {
        text: {
            type: String,
            default: ''
        },
        maxLength: {
            type: Number,
            default: 6
        }
    },
    data() {
        return {
            expanded: false
        }
    },
    computed: {
        displayText() {
            if (!this.text) return '';
            if (this.text.length <= this.maxLength || this.expanded) {
                return this.text;
            }
            return this.text.substring(0, this.maxLength) + '...';
        },
        needExpand() {
            return this.text && this.text.length > this.maxLength;
        }
    },
    render(h) {
        return h('span', [
            this.displayText,
            this.needExpand ? h('el-button', {
                props: {
                    type: 'text',
                    size: 'mini'
                },
                style: {
                    padding: '0',
                    marginLeft: '4px',
                    fontSize: '12px'
                },
                on: {
                    click: () => { this.expanded = !this.expanded }
                }
            }, this.expanded ? '收起' : '展开') : null
        ])
    }
}

export default {
    name: "DeviceData",
    dicts: ['material_category', 'material_level', 'risk_level', 'yes_no', 'medical_insurance_category'],
    components: {
        OperateDia,
        PdfUpload,
        ExpandableText
    },
    data() {
        return {
            loading: true,
            ids: [],
            single: true,
            multiple: true,
            showSearch: false,
            total: 0,
            deviceList: [],
            excelDate: [],
            title: "",
            open: false,
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                barcode: null,
                hospitalCode: null,
                materialName: null,
                manufacturer: null,
                materialCategory: null,
                isHighValue: null
            },
            userProfile: {},
        };
    },
    created() {
        this.getList();
        this.getUserProfile();
    },
    methods: {
        /** 查询设备信息列表 */
        getList() {
            this.loading = true;
            listDevice(this.queryParams).then(res => {
                this.deviceList = res.data.pagedData || [];
                this.excelDate = res.data.allData || [];
                this.total = res.data.total || 0;
            }).finally(() => {
                this.loading = false;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$refs.operateDia.show('add');
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            const id = row.id || this.ids
            this.$refs.operateDia.show('edit', id);
        },
        /** 证件管理按钮操作 */
        handleUpload(row) {
            let deviceId;
            let rowData = null;
            if (row && row.id) {
                deviceId = row.id;
                rowData = row;
            } else if (this.ids && this.ids.length > 0) {
                deviceId = this.ids[0];
                rowData = this.deviceList.find(item => item.id === deviceId);
            } else {
                this.$modal.msgWarning('请选择要上传PDF的设备！');
                return;
            }
            this.$refs.pdfUpload.show('upload', deviceId, rowData);
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row && row.id ? [row.id] : this.ids;
            if (!ids || ids.length === 0) {
                this.$modal.msgWarning('请选择要删除的数据');
                return;
            }
            this.$modal.confirm('是否确认删除设备信息编号为"' + ids + '"的数据项？').then(() => {
                return delDevice(ids);
            }).then((res) => {
                if (res.code === 200 || res.code === '200') {
                    this.$modal.msgSuccess(res.message || '删除成功');
                    this.getList();
                } else {
                    this.$modal.msgError(res.message || '删除失败');
                }
            }).catch(() => { });
        },
        /** 详情按钮操作 */
        handleDetail(row) {
            this.$refs.operateDia.show('detail', row.id);
        },
        /** 下拉菜单命令处理 */
        handleCommand(command) {
            const { action, row } = command;
            switch (action) {
                case 'detail':
                    this.handleDetail(row);
                    break;
                case 'edit':
                    this.handleUpdate(row);
                    break;
                case 'upload':
                    this.handleUpload(row);
                    break;
                case 'delete':
                    this.handleDelete(row);
                    break;
            }
        },
        /** 获取当前登录用户信息 */
        getUserProfile() {
            getUserProfile().then(res => {
                this.userProfile = res.data || {};
            });
        },
        /** 获取字典标签 */
        getDictLabel(dictType, value) {
            if (!value) return '';
            const dict = this.dict.type[dictType];
            if (!dict) return value;
            const item = dict.find(d => d.value === value);
            return item ? item.label : value;
        },
        /** 根据字典标签获取值 */
        getDictValue(dictType, label) {
            if (!label) return '';
            const dict = this.dict.type[dictType];
            if (!dict) return '';
            const item = dict.find(d => d.label === label);
            return item ? item.value : '';
        },

        /** 解析价格 */
        parsePrice(value) {
            if (!value) return null;
            const str = String(value).replace(/[￥¥,，]/g, '');
            const num = parseFloat(str);
            return isNaN(num) ? null : num;
        },
        /** 解析日期 */
        parseDate(value) {
            if (!value) return '';
            try {
                if (typeof value === 'number') {
                    const date = new Date((value - 25569) * 86400 * 1000);
                    return date.toISOString().split('T')[0];
                }
                const date = new Date(value);
                if (isNaN(date.getTime())) return '';
                return date.toISOString().split('T')[0];
            } catch (error) {
                return '';
            }
        },
        /** 导入按钮操作 */
        handleImport() {
            this.$refs.fileInput.click();
        },
        /** 文件选择处理 */
        handleFileChange(event) {
            const file = event.target.files[0];
            if (!file) return;
            const fileName = file.name.toLowerCase();
            if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                this.$modal.msgError('请选择Excel文件（.xlsx或.xls格式）！');
                return;
            }
            if (file.size > 10 * 1024 * 1024) {
                this.$modal.msgError('文件大小不能超过10MB！');
                return;
            }
            this.importExcel(file);
        },
        /** 导入Excel */
        importExcel(file) {
            const loading = this.$loading({
                lock: true,
                text: '正在导入数据...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            import('xlsx').then(xlsx => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = new Uint8Array(e.target.result);
                        const workbook = xlsx.read(data, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const jsonData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });
                        if (jsonData.length < 2) {
                            loading.close();
                            this.$modal.msgError("Excel文件中没有有效数据！");
                            return;
                        }
                        this.processImportData(jsonData);
                        loading.close();
                    } catch (error) {
                        loading.close();
                        this.$modal.msgError("Excel文件格式错误，请检查文件！");
                    }
                };
                reader.readAsArrayBuffer(file);
            }).catch(() => {
                loading.close();
                this.$modal.msgError("导入功能需要xlsx库支持！");
            });
            this.$refs.fileInput.value = '';
        },
        /** 处理导入数据 */
        processImportData(jsonData) {
            const headers = jsonData[0];
            const dataRows = jsonData.slice(1);
            const requiredHeaders = ['条形码号', '院内编码', '耗材名称'];
            const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
            if (missingHeaders.length > 0) {
                this.$modal.msgError(`Excel文件缺少必要列：${missingHeaders.join('、')}`);
                return;
            }
            const importedData = [];
            const errors = [];
            dataRows.forEach((row, index) => {
                if (row.length === 0 || !row.some(cell => cell)) return;
                const rowData = {};
                headers.forEach((header, colIndex) => {
                    const value = row[colIndex];
                    switch (header) {
                        case '条形码号':
                            rowData.barcode = value || '';
                            break;
                        case '院内编码':
                            rowData.hospitalCode = value || '';
                            break;
                        case '27位医保码':
                            rowData.medicalInsuranceCode = value || '';
                            break;
                        case '耗材名称':
                            rowData.materialName = value || '';
                            break;
                        case '通用名':
                            rowData.genericName = value || '';
                            break;
                        case '规格':
                            rowData.specification = value || '';
                            break;
                        case '型号':
                            rowData.modelxh = value || '';
                            break;
                        case '单位':
                            rowData.unit = value || '';
                            break;
                        case '注册证号':
                            rowData.registrationNumber = value || '';
                            break;
                        case '厂家':
                            rowData.manufacturer = value || '';
                            break;
                        case '批号':
                            rowData.batchNumber = value || '';
                            break;
                        case '功能用途':
                            rowData.functionalUse = value || '';
                            break;
                        case '是否采集':
                            rowData.isCollected = this.getDictValue('yes_no', value);
                            break;
                        case '是否18种特定':
                            rowData.isSpecific18 = this.getDictValue('yes_no', value);
                            break;
                        case '是否植入性耗材':
                            rowData.isImplantable = this.getDictValue('yes_no', value);
                            break;
                        case '是否一次性使用':
                            rowData.isDisposable = this.getDictValue('yes_no', value);
                            break;
                        case '是否灭菌':
                            rowData.isSterile = this.getDictValue('yes_no', value);
                            break;
                        case '集采时间':
                            rowData.collectionTime = this.parseDate(value);
                            break;
                        case '包装':
                            rowData.packaging = value || '';
                            break;
                        case '医保类别':
                            rowData.medicalInsuranceCategory = this.getDictValue('medical_insurance_category', value);
                            break;
                        case '收费条码':
                            rowData.chargingBarcode = value || '';
                            break;
                        case '调价信息':
                            rowData.priceAdjustmentInfo = value || '';
                            break;
                        case '供应商':
                            rowData.supplier = value || '';
                            break;
                        case '适用科室':
                            rowData.applicableDepartment = value || '';
                            break;
                        case '入库价格':
                            rowData.purchasePrice = this.parsePrice(value);
                            break;
                        case '售价':
                            rowData.salePrice = this.parsePrice(value);
                            break;
                        case '有效期':
                            rowData.expiryDate = this.parseDate(value);
                            break;
                        case '温湿度':
                            rowData.temperatureHumidity = value || '';
                            break;
                        case '物资分类':
                            rowData.materialCategory = this.getDictValue('material_category', value);
                            break;
                        case '医用耗材级别':
                            rowData.materialLevel = this.getDictValue('material_level', value);
                            break;
                        case '风险等级':
                            rowData.riskLevel = this.getDictValue('risk_level', value);
                            break;
                        case '是否高值':
                            rowData.isHighValue = this.getDictValue('yes_no', value);
                            break;
                        case '是否可收费':
                            rowData.isChargeable = this.getDictValue('yes_no', value);
                            break;
                    }
                });
                if (!rowData.barcode || !rowData.hospitalCode || !rowData.materialName) {
                    errors.push(`第${index + 2}行：条形码号、院内编码、耗材名称为必填项`);
                } else {
                    importedData.push(rowData);
                }
            });
            if (errors.length > 0) {
                this.$modal.msgError(`导入失败，发现以下错误：\n${errors.slice(0, 5).join('\n')}${errors.length > 5 ? '\n...' : ''}`);
                return;
            }
            if (importedData.length === 0) {
                this.$modal.msgError("没有找到有效的数据行！");
                return;
            }
            this.$modal.confirm(`确认导入${importedData.length}条数据吗？`).then(() => {
                importDevice(importedData).then(res => {
                    if (res.code === 200 || res.code === '200') {
                        this.$modal.msgSuccess(res.message || '导入数据成功');
                        this.getList();
                    } else if (res.code === 400 || res.code === '400') {
                        this.$modal.msgError(res.message || '导入失败');
                    } else {
                        this.$modal.msgError(res.message || '导入失败，请重试');
                    }
                }).catch(() => {
                    this.$modal.msgError('导入失败，请检查网络连接');
                });
            }).catch(() => {
                this.$modal.msg('已取消导入');
            });
        },
        /** 下载模板 */
        downloadTemplate() {
            const templateData = [{
                '序号': 1,
                '条形码号': '1234567890123',
                '院内编码': 'YN001',
                '27位医保码': '123456789012345678901234567',
                '物资分类': '医用耗材',
                '医用耗材级别': '一级',
                '风险等级': '低风险',
                '耗材名称': '一次性使用无菌注射器',
                '通用名': '注射器',
                '规格': '5ml',
                '型号': 'BD-5ML',
                '单位': '支',
                '注册证号': '国械注准20123456789',
                '厂家': '北京医疗器械有限公司',
                '批号': '20240101',
                '功能用途': '用于药物注射',
                '是否可收费': '是',
                '是否采集': '是',
                '是否高值': '否',
                '是否18种特定': '否',
                '是否植入性耗材': '否',
                '是否一次性使用': '是',
                '是否灭菌': '是',
                '集采时间': '2024-01-01',
                '包装': '单支包装',
                '医保类别': '甲类',
                '收费条码': '1234567890123',
                '入库价格': '￥2.50',
                '售价': '￥3.00',
                '调价信息': '',
                '供应商': '北京医疗用品供应商',
                '适用科室': '内科,外科',
                '有效期': '2025-12-31',
                '温湿度': '常温干燥'
            }];
            this.exportToExcel(templateData, '设备信息导入模板');
            this.$modal.msgSuccess('模板下载成功！');
        },
        /** 导出按钮操作 */
        handleExport() {
            try {
                const exportData = this.excelDate.map((item, index) => {
                    return {
                        '序号': index + 1,
                        '条形码号': item.barcode || '',
                        '院内编码': item.hospitalCode || '',
                        '27位医保码': item.medicalInsuranceCode || '',
                        '物资分类': this.getDictLabel('material_category', item.materialCategory),
                        '医用耗材级别': this.getDictLabel('material_level', item.materialLevel),
                        '风险等级': this.getDictLabel('risk_level', item.riskLevel),
                        '耗材名称': item.materialName || '',
                        '通用名': item.genericName || '',
                        '规格': item.specification || '',
                        '型号': item.modelxh || '',
                        '单位': item.unit || '',
                        '注册证号': item.registrationNumber || '',
                        '厂家': item.manufacturer || '',
                        '批号': item.batchNumber || '',
                        '功能用途': item.functionalUse || '',
                        '是否可收费': this.getDictLabel('yes_no', item.isChargeable),
                        '是否采集': this.getDictLabel('yes_no', item.isCollected),
                        '是否高值': this.getDictLabel('yes_no', item.isHighValue),
                        '是否18种特定': this.getDictLabel('yes_no', item.isSpecific18),
                        '是否植入性耗材': this.getDictLabel('yes_no', item.isImplantable),
                        '是否一次性使用': this.getDictLabel('yes_no', item.isDisposable),
                        '是否灭菌': this.getDictLabel('yes_no', item.isSterile),
                        '集采时间': item.collectionTime ? this.parseTime(item.collectionTime, '{y}-{m}-{d}') : '',
                        '包装': item.packaging || '',
                        '医保类别': this.getDictLabel('medical_insurance_category', item.medicalInsuranceCategory),
                        '收费条码': item.chargingBarcode || '',
                        '入库价格': item.purchasePrice ? `￥${item.purchasePrice}` : '',
                        '售价': item.salePrice ? `￥${item.salePrice}` : '',
                        '调价信息': item.priceAdjustmentInfo || '',
                        '供应商': item.supplier || '',
                        '适用科室': item.applicableDepartment || '',
                        '有效期': item.expiryDate ? this.parseTime(item.expiryDate, '{y}-{m}-{d}') : '',
                        '温湿度': item.temperatureHumidity || ''
                    };
                });

                this.exportToExcel(exportData, '医用耗材信息数据');
                this.$modal.msgSuccess('导出成功！');
            } catch (error) {
                this.$modal.msgError('导出失败，请重试！');
            }
        },
        /** 导出Excel */
        exportToExcel(data, fileName) {
            import('xlsx').then(xlsx => {
                const wb = xlsx.utils.book_new();
                const ws = xlsx.utils.json_to_sheet(data);
                const colWidths = [
                    { wch: 8 },   // 序号
                    { wch: 15 },  // 条形码号
                    { wch: 12 },  // 院内编码
                    { wch: 20 },  // 27位医保码
                    { wch: 12 },  // 物资分类
                    { wch: 15 },  // 医用耗材级别
                    { wch: 10 },  // 风险等级
                    { wch: 20 },  // 耗材名称
                    { wch: 15 },  // 通用名
                    { wch: 12 },  // 规格
                    { wch: 12 },  // 型号
                    { wch: 8 },   // 单位
                    { wch: 18 },  // 注册证号
                    { wch: 20 },  // 厂家
                    { wch: 12 },  // 批号
                    { wch: 15 },  // 功能用途
                    { wch: 12 },  // 是否可收费
                    { wch: 10 },  // 是否采集
                    { wch: 10 },  // 是否高值
                    { wch: 12 },  // 是否18种特定
                    { wch: 15 },  // 是否植入性耗材
                    { wch: 15 },  // 是否一次性使用
                    { wch: 10 },  // 是否灭菌
                    { wch: 12 },  // 集采时间
                    { wch: 12 },  // 包装
                    { wch: 12 },  // 医保类别
                    { wch: 15 },  // 收费条码
                    { wch: 12 },  // 入库价格
                    { wch: 12 },  // 售价
                    { wch: 15 },  // 调价信息
                    { wch: 20 },  // 供应商
                    { wch: 15 },  // 适用科室
                    { wch: 12 },  // 有效期
                    { wch: 12 }   // 温湿度
                ];
                ws['!cols'] = colWidths;
                xlsx.utils.book_append_sheet(wb, ws, '设备信息');
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const fullFileName = `${fileName}_${timestamp}.xlsx`;
                xlsx.writeFile(wb, fullFileName);
            }).catch(() => {
                this.$modal.msgError("请联系管理员！");
            });
        },

        /** 处理上传状态刷新 */
        handleRefreshUploadStatus(deviceId) {
            // 刷新列表数据以获取最新的文件状态
            this.getList();

            // 如果PDF上传弹框仍然打开，更新其状态
            if (this.$refs.pdfUpload && this.$refs.pdfUpload.visible) {
                const rowData = this.deviceList.find(item => item.id === deviceId);
                if (rowData) {
                    this.$refs.pdfUpload.updateUploadedStatusFromRow(rowData);
                }
            }
        },

        /** 分页大小改变 */
        handleSizeChange(val) {
            this.queryParams.pageSize = val;
            this.getList();
        },

        /** 当前页改变 */
        handleCurrentChange(val) {
            this.queryParams.pageNum = val;
            this.getList();
        }
    }
};
</script>

<style lang="scss" scoped>
.device-management-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 10px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .page-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;

            i {
                font-size: 28px;
            }
        }

        .page-subtitle {
            font-size: 15px;
            opacity: 0.9;
            margin-top: 2px;
        }

        .toggle-search-btn {
            color: #fff;
            font-size: 16px;
            padding: 0 4px;

            &:hover {
                color: #f0f9ff;
            }

            i {
                margin-right: 3px;
            }
        }
    }

    .search-section,
    .toolbar-section {
        background: #fff;
        border-bottom: 1px solid #e4e7ed;
        padding: 6px 16px;

        .search-form,
        .toolbar-left {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            ::v-deep .el-form-item {
                margin-right: 6px;
                margin-bottom: 2px;

                .el-form-item__label {
                    font-weight: 500;
                    color: #606266;
                    font-size: 15px;
                }

                .el-input__inner,
                .el-button {
                    height: 28px;
                    font-size: 15px;
                    border-radius: 4px;
                }

                .el-input__inner:focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
                }

                .el-button {
                    padding: 0 6px;
                }
            }
        }
    }

    .table-container {
        flex: 1;
        overflow-x: auto;
        overflow-y: auto;
        background: #fff;
        min-width: 600px;

        &::-webkit-scrollbar {
            height: 12px;
        }

        &::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;

            &:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        }

        .table-header {
            padding: 4px 10px;
            border-bottom: 1px solid #e4e7ed;

            .table-title {
                display: flex;
                align-items: center;
                gap: 4px;
                font-weight: 600;
                color: #303133;
                font-size: 18px;

                i {
                    color: #667eea;
                    font-size: 18px;
                }
            }
        }

        .main-table {
            min-width: 2500px;

            ::v-deep .el-table__body-wrapper,
            ::v-deep .el-table__header-wrapper {
                overflow: visible !important;
            }

            ::v-deep .el-table {
                table-layout: auto !important;
            }

            ::v-deep .el-table__header th {
                background: #fafafa;
                font-weight: 600;
                font-size: 15px;
                height: 36px;
            }

            ::v-deep .el-table__body tr:hover {
                background: #f5f7fa;
            }

            ::v-deep .el-table__body td {
                font-size: 15px;
                height: 36px;
            }

            ::v-deep .el-dropdown .el-button {
                padding: 2px 6px;
                font-size: 15px;
                height: 28px;
            }
        }
    }

    .pagination-wrapper {
        background: #fff;
        padding: 4px 10px;
        border-top: 1px solid #e4e7ed;
        text-align: center;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);

        ::v-deep .el-pagination {

            .el-pagination__total,
            .el-pagination__jump {
                font-size: 15px;
            }

            .el-pager li,
            .btn-prev,
            .btn-next {
                font-size: 15px;
                min-width: 28px;
                height: 28px;
                line-height: 28px;
            }
        }
    }

    .price-text {
        color: #e67e22;
        font-weight: 600;
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .device-management-page {
        .page-header .page-title {
            font-size: 20px;
        }

        .search-section,
        .toolbar-section {

            .search-form,
            .toolbar-left {
                ::v-deep .el-form-item {
                    .el-form-item__label {
                        font-size: 13px;
                    }

                    .el-input__inner,
                    .el-button {
                        height: 22px;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}
</style>