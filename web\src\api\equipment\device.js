import request from '@/utils/request'

// 查询设备信息列表
export function listDevice(query) {
  return request({
    url: '/equipment/listDevice',
    method: 'get',
    params: query
  })
}

// 查询设备信息详细
export function getDevice(query) {
  return request({
    url: '/equipment/getDevice',
    method: 'get',
    params: query
  })
}

// 新增设备信息
export function addDevice(data) {
  return request({
    url: '/equipment/addDevice',
    method: 'post',
    data: data
  })
}

// 修改设备信息
export function updateDevice(data) {
  return request({
    url: '/equipment/updateDevice',
    method: 'post',
    data: data
  })
}

// 删除设备信息
export function delDevice(data) {
  return request({
    url: '/equipment/delDevice',
    method: 'post',
    data: data
  })
}

// 导入设备信息
export function importDevice(data) {
  return request({
    url: '/equipment/importDevice',
    method: 'post',
    data: data
  })
}


