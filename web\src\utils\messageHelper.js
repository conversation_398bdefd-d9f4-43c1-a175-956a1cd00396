/**
 * 消息提示工具类
 * 统一处理各种消息提示，支持多种后端返回字段格式
 */

/**
 * 显示消息提示
 * @param {Object} modal - Vue实例的$modal对象
 * @param {string} type - 消息类型：success, error, warning, info
 * @param {Object|string} response - 后端响应对象或直接的消息字符串
 * @param {string} defaultMsg - 默认消息
 */
export function showMessage(modal, type, response, defaultMsg = '') {
    let message = defaultMsg;
    
    // 如果response是字符串，直接使用
    if (typeof response === 'string') {
        message = response;
    } else if (response && typeof response === 'object') {
        // 支持多种字段名：message, msg, data.message, data.msg
        message = response.message || 
                 response.msg || 
                 response.data?.message || 
                 response.data?.msg || 
                 defaultMsg;
    }
    
    switch (type) {
        case 'success':
            modal.msgSuccess(message);
            break;
        case 'error':
            modal.msgError(message);
            break;
        case 'warning':
            modal.msgWarning(message);
            break;
        case 'info':
            modal.msg(message);
            break;
        default:
            modal.msg(message);
    }
}

/**
 * 处理API响应的统一方法
 * @param {Object} modal - Vue实例的$modal对象
 * @param {Object} response - 后端响应对象
 * @param {Function} successCallback - 成功回调函数
 * @param {Function} errorCallback - 失败回调函数
 * @param {string} successMsg - 成功默认消息
 * @param {string} errorMsg - 失败默认消息
 * @returns {boolean} - 是否成功
 */
export function handleApiResponse(modal, response, successCallback = null, errorCallback = null, successMsg = '操作成功', errorMsg = '操作失败') {
    const code = response?.code || response?.status;
    
    if (code === 200 || code === '200') {
        showMessage(modal, 'success', response, successMsg);
        if (successCallback && typeof successCallback === 'function') {
            successCallback(response);
        }
        return true;
    } else if (code === 400 || code === '400') {
        showMessage(modal, 'error', response, errorMsg);
        if (errorCallback && typeof errorCallback === 'function') {
            errorCallback(response);
        }
        return false;
    } else {
        showMessage(modal, 'error', response, `${errorMsg}，请重试`);
        if (errorCallback && typeof errorCallback === 'function') {
            errorCallback(response);
        }
        return false;
    }
}

/**
 * 处理网络错误
 * @param {Object} modal - Vue实例的$modal对象
 * @param {Error} error - 错误对象
 * @param {string} defaultMsg - 默认错误消息
 */
export function handleNetworkError(modal, error, defaultMsg = '网络连接异常，请重试') {
    console.error('网络错误:', error);
    showMessage(modal, 'error', null, defaultMsg);
}

/**
 * Vue mixin，为组件添加消息提示方法
 */
export const MessageMixin = {
    methods: {
        /**
         * 显示消息提示
         */
        showMessage(type, response, defaultMsg = '') {
            showMessage(this.$modal, type, response, defaultMsg);
        },
        
        /**
         * 处理API响应
         */
        handleApiResponse(response, successCallback = null, errorCallback = null, successMsg = '操作成功', errorMsg = '操作失败') {
            return handleApiResponse(this.$modal, response, successCallback, errorCallback, successMsg, errorMsg);
        },
        
        /**
         * 处理网络错误
         */
        handleNetworkError(error, defaultMsg = '网络连接异常，请重试') {
            handleNetworkError(this.$modal, error, defaultMsg);
        },
        
        /**
         * 快捷方法：成功提示
         */
        showSuccess(message) {
            this.showMessage('success', message);
        },
        
        /**
         * 快捷方法：错误提示
         */
        showError(message) {
            this.showMessage('error', message);
        },
        
        /**
         * 快捷方法：警告提示
         */
        showWarning(message) {
            this.showMessage('warning', message);
        },
        
        /**
         * 快捷方法：信息提示
         */
        showInfo(message) {
            this.showMessage('info', message);
        }
    }
};

export default {
    showMessage,
    handleApiResponse,
    handleNetworkError,
    MessageMixin
};
