import request from '@/utils/request'
const baseURL = "Supplier/";
// 查询数据
export function SelectSupplier(query) {
    return request({
      url: baseURL + "SelectSupplier",
        method: 'get',
        params: query
    })
}
// 修改数据
export function AddSupplier(data) {
  return request({
    url: baseURL + "AddSupplier",
     method: 'post',
    data: data
  })
}
// 移除数据
export function DeletManufacturer(data) {
  return request({
    url: baseURL + "DeletManufacturer",
     method: 'post',
    data: data
  })
}