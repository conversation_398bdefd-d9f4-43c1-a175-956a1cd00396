<template>
  <div class="supplier-management app-container">
    <!-- 标题和操作按钮 -->
  
  
    <!-- 搜索区域 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="供应商编码">
        <el-input v-model="searchForm.supplierCode" placeholder="请输入编码"></el-input>
      </el-form-item>
      <el-form-item label="供应商名称">
        <el-input v-model="searchForm.supplierName" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="供应商类别">
        <el-select v-model="searchForm.supplierCategory" placeholder="请选择">
          <el-option label="全部" value=""></el-option>
          <el-option label="01 供应商" value="01"></el-option>
          <el-option label="02 经销商" value="02"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
        <el-button type="primary" @click="handleAdd">新增供应商</el-button>
      </el-form-item>
    </el-form>
  
    <!-- 供应商列表 -->
    <el-table :data="supplierList" border stripe style="width: 100%; margin-top: 10px">
      <el-table-column prop="supplierCode" label="供应商编码"></el-table-column>
      <el-table-column prop="supplierName" label="供应商名称"></el-table-column>
      <el-table-column prop="supplierCategory" label="供应商类别">
        <template slot-scope="scope">
          <span>{{ scope.row.supplierCategory === '01' ? '供应商' : '经销商' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="contactPerson" label="联系人"></el-table-column>
      <el-table-column prop="phone" label="电话"></el-table-column>
      <el-table-column prop="mobile" label="手机"></el-table-column>
      <el-table-column prop="email" label="EMAIL"></el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  
    <!-- 分页 -->
    <div class="pagination" v-if="total > 0">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
        :total="total"></el-pagination>
    </div>
  
    <!-- 供应商表单弹窗 -->
    <el-dialog :title="isEdit ? '编辑供应商' : '新增供应商'" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false">
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商编码" prop="supplierCode">
              <el-input v-model="formData.supplierCode" :disabled="isEdit"></el-input>
            </el-form-item>
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="formData.supplierName"></el-input>
            </el-form-item>
            <el-form-item label="纳税人登记号" prop="taxpayerNumber">
              <el-input v-model="formData.taxpayerNumber"></el-input>
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input v-model="formData.address"></el-input>
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input v-model="formData.phone"></el-input>
            </el-form-item>
            <el-form-item label="EMAIL" prop="email">
              <el-input v-model="formData.email"></el-input>
            </el-form-item>
          </el-col>
  
          <el-col :span="12">
            <el-form-item label="供应商类别" prop="supplierCategory">
              <el-select v-model="formData.supplierCategory">
                <el-option label="01 供应商" value="01"></el-option>
                <el-option label="02 经销商" value="02"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属行业" prop="industry">
              <el-input v-model="formData.industry"></el-input>
            </el-form-item>
            <el-form-item label="省" prop="province">
              <el-input v-model="formData.province"></el-input>
            </el-form-item>
            <el-form-item label="市" prop="city">
              <el-input v-model="formData.city"></el-input>
            </el-form-item>
            <el-form-item label="法人" prop="legalPerson">
              <el-input v-model="formData.legalPerson"></el-input>
            </el-form-item>
            <el-form-item label="传真" prop="fax">
              <el-input v-model="formData.fax"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
  
        <!-- 联系人信息 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="formData.contactPerson"></el-input>
            </el-form-item>
            <el-form-item label="手机" prop="mobile">
              <el-input v-model="formData.mobile"></el-input>
            </el-form-item>
            <el-form-item label="呼机" prop="pager">
              <el-input v-model="formData.pager"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
  
        <!-- 其他信息 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input v-model="formData.bankAccount"></el-input>
            </el-form-item>
            <el-form-item label="扣率" prop="discountRate">
              <el-input v-model="formData.discountRate"></el-input>
            </el-form-item>
            <el-form-item label="信用等级" prop="creditLevel">
              <el-input v-model="formData.creditLevel"></el-input>
            </el-form-item>
            <el-form-item label="到货地址" prop="deliveryAddress">
              <el-input v-model="formData.deliveryAddress"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
  
        <!-- 保存按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="resetForm">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { SelectSupplier, AddSupplier, DeletSupplier } from '@/api/base/supplier';
export default {

  name: 'SupplierManagement',
  data() {
    return {
      // 搜索表单
      searchForm: {
        supplierCode: '',
        supplierName: '',
        supplierCategory: ''
      },
      // 供应商列表数据
      supplierList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,

      // 表单弹窗相关
      dialogVisible: false,
      isEdit: false,
      formData: {
        supplierCode: '',
        supplierName: '',
        taxpayerNumber: '',
        address: '',
        phone: '',
        email: '',
        supplierCategory: '',
        industry: '',
        province: '',
        city: '',
        legalPerson: '',
        fax: '',
        contactPerson: '',
        mobile: '',
        pager: '',
        bankAccount: '',
        discountRate: '',
        creditLevel: '',
        deliveryAddress: ''
      },
      // 表单验证规则
      rules: {
        supplierCode: [
          { required: true, message: '请输入供应商编码', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        supplierName: [
          { required: true, message: '请输入供应商名称', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        supplierCategory: [
          { required: true, message: '请选择供应商类别', trigger: 'change' }
        ],
        phone: [
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: ['blur', 'change'] }
        ],
        mobile: [
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 初始化加载数据
    this.loadSupplierList();
  },
  methods: {
    // 加载供应商列表
    loadSupplierList() {
      // 这里模拟API请求，实际项目中替换为真实接口调用
      // 合并分页参数和搜索参数
      const params = {
        page: this.currentPage,
        size: this.pageSize,
        ...this.searchForm
      };

      // 模拟API请求
      console.log('加载供应商列表参数:', params);

      // 模拟返回数据
      this.supplierList = [
        {
          supplierCode: 'SP001',
          supplierName: 'ABC供应商',
          taxpayerNumber: '91310101MA********',
          address: '上海市浦东新区XX路XX号',
          phone: '021-********',
          email: '<EMAIL>',
          supplierCategory: '01',
          industry: '电子制造',
          province: '上海市',
          city: '上海市',
          legalPerson: '张三',
          fax: '021-********',
          contactPerson: '李四',
          mobile: '***********',
          pager: '',
          bankAccount: '622202********90123',
          discountRate: '0.95',
          creditLevel: 'A',
          deliveryAddress: '上海市浦东新区XX路XX号'
        },
        {
          supplierCode: 'SP002',
          supplierName: 'XYZ经销商',
          taxpayerNumber: '91310101MA********',
          address: '北京市朝阳区XX路XX号',
          phone: '010-********',
          email: '<EMAIL>',
          supplierCategory: '02',
          industry: '贸易',
          province: '北京市',
          city: '北京市',
          legalPerson: '王五',
          fax: '010-********',
          contactPerson: '赵六',
          mobile: '***********',
          pager: '',
          bankAccount: '6222029********0987',
          discountRate: '0.90',
          creditLevel: 'B',
          deliveryAddress: '北京市朝阳区XX路XX号'
        }
      ];
      this.total = 2; // 模拟总条数
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1; // 重置页码
      this.loadSupplierList();
    },

    // 重置搜索条件
    resetSearch() {
      this.searchForm = {
        supplierCode: '',
        supplierName: '',
        supplierCategory: ''
      };
      this.currentPage = 1;
      this.loadSupplierList();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.loadSupplierList();
    },

    // 当前页码改变
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadSupplierList();
    },

    // 新增供应商
    handleAdd() {
      this.isEdit = false;
      this.formData = {
        supplierCode: '',
        supplierName: '',
        taxpayerNumber: '',
        address: '',
        phone: '',
        email: '',
        supplierCategory: '',
        industry: '',
        province: '',
        city: '',
        legalPerson: '',
        fax: '',
        contactPerson: '',
        mobile: '',
        pager: '',
        bankAccount: '',
        discountRate: '',
        creditLevel: '',
        deliveryAddress: ''
      };
      this.dialogVisible = true;
    },

    // 编辑供应商
    handleEdit(row) {
      this.isEdit = true;
      // 深拷贝，避免直接修改表格数据
      this.formData = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },

    // 删除供应商
    handleDelete(row) {
      this.$confirm(`确定要删除供应商【${row.supplierName}】吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里模拟API请求，实际项目中替换为真实接口调用
        console.log('删除供应商:', row.supplierCode);

        // 模拟删除成功
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.loadSupplierList();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    // 提交表单
    submitForm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 表单验证通过，提交数据
          if (this.isEdit) {
            // 编辑操作
            console.log('更新供应商:', this.formData);

            // 模拟API请求成功
            this.$message({
              type: 'success',
              message: '更新成功!'
            });
          } else {
            // 新增操作
            console.log('新增供应商:', this.formData);

            // 模拟API请求成功
            this.$message({
              type: 'success',
              message: '新增成功!'
            });
          }
          this.dialogVisible = false;
          this.loadSupplierList();
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.formRef.resetFields();
      this.dialogVisible = false;
    }
  }
};
</script>

<style scoped>
.header-operation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.search-form {}

.pagination {
  margin-top: 15px;
  text-align: right;
}
</style>
