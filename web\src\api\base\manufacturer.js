import request from '@/utils/request'
const baseURL = "Manufacturer/";
// 查询数据
export function SelectManufacturer(query) {
    return request({
      url: baseURL + "SelectManufacturer",
        method: 'get',
        params: query
    })
}
// 修改数据
export function AddManufacturer(data) {
  return request({
    url: baseURL + "AddManufacturer",
     method: 'post',
    data: data
  })
}
// 移除数据
export function DeletManufacturer(data) {
  return request({
    url: baseURL + "DeletManufacturer",
     method: 'post',
    data: data
  })
}