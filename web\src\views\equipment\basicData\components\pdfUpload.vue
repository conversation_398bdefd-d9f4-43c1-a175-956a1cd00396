<template>
    <!-- PDF文件上传弹框页面 -->
    <el-dialog :title="title" :visible.sync="visible" width="50%" :close-on-click-modal="false"
        :close-on-press-escape="false" append-to-body class="pdf-upload-dialog">
        <!-- PDF文件类型选择 -->
        <div class="pdf-type-selector">
            <div class="selector-header">
                <h4>请选择要上传的PDF文件类型</h4>
                <p class="selector-tip">
                    选择对应的文件类型，然后上传相应的PDF文件
                    <span class="upload-summary">
                        (已上传: {{ getUploadedCount() }}/{{ pdfTypes.length }})
                    </span>
                </p>
            </div>
            <div class="type-options">
                <div class="type-item" v-for="type in pdfTypes" :key="type.value"
                    :class="{ 'selected': selectedType === type.value, 'uploaded': type.uploaded }"
                    @click="selectType(type.value)">
                    <div class="type-content">
                        <div class="type-info">
                            <i :class="type.icon"></i>
                            <span class="type-name">{{ type.label }}</span>
                        </div>
                        <div class="type-status">
                            <el-tag v-if="type.uploaded" type="success" size="mini">已上传</el-tag>
                            <el-tag v-else type="info" size="mini">未上传</el-tag>
                        </div>
                    </div>
                    <!-- 将操作按钮移到单独的行，避免影响label显示 -->
                    <div v-if="type.uploaded && type.filePath" class="file-actions">
                        <el-button type="text" size="mini" @click.stop="viewFile(type)" class="action-btn view-btn">
                            <i class="el-icon-view"></i> 查看
                        </el-button>
                        <el-button type="text" size="mini" @click.stop="deleteFile(type)" class="action-btn delete-btn">
                            <i class="el-icon-delete"></i> 删除
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- PDF文件上传区域 -->
        <div class="pdf-upload-wrapper" v-if="selectedType">
            <div class="upload-header">
                <h4>上传 {{ getCurrentTypeName() }} PDF文件</h4>
            </div>
            <el-upload ref="pdfUpload" class="pdf-upload" drag :action="uploadUrl" :headers="uploadHeaders"
                :data="uploadData" :file-list="fileList" :on-change="handleFileChange" :on-remove="handleRemove"
                :on-exceed="handleExceed" :limit="maxFiles" :auto-upload="false" accept=".pdf" multiple>
                <div class="upload-content">
                    <i class="el-icon-document upload-icon"></i>
                    <div class="upload-text">
                        <p class="main-text">将PDF文件拖到此处，或<em>点击上传</em></p>
                        <p class="sub-text">支持多个文件同时上传</p>
                    </div>
                </div>
                <div class="el-upload__tip" slot="tip">
                    <div class="upload-tips">
                        <i class="el-icon-info"></i>
                        <span>只能上传PDF文件，单个文件大小不超过{{ maxSize }}MB，最多上传{{ maxFiles }}个文件</span>
                    </div>
                </div>
            </el-upload>

            <!-- 上传进度 -->
            <div v-if="uploading" class="upload-progress">
                <el-progress :percentage="uploadProgress" :status="progressStatus || null"></el-progress>
                <p class="progress-text">{{ progressText }}</p>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">关闭</el-button>
            <el-button v-if="selectedType" type="primary" @click="handleConfirm"
                :disabled="!fileList || fileList.length === 0">
                上传{{ getCurrentTypeName() }} ({{ fileList ? fileList.length : 0 }})
            </el-button>
            <el-button v-if="selectedType && fileList && fileList.length > 0" type="warning" @click="handleClearAll">
                清空文件
            </el-button>
        </div>
    </el-dialog>
</template>

<script>
import { EquipUploadPdf, DeleteEquipPdf } from '@/api/equipment/pdfUpload'
import { getToken } from '@/utils/auth'

export default {
    name: "PdfUpload",
    data() {
        return {
            // 弹出层标题
            title: "PDF文件上传",
            // 是否显示弹出层
            visible: false,
            // 操作类型
            operateType: '',
            // 当前设备ID
            currentDeviceId: null,
            // 文件列表
            fileList: [],
            // 上传地址
            uploadUrl: process.env.VUE_APP_BASE_API + '/EquipmentUpload/EquipUploadPdf',
            // 上传头部信息
            uploadHeaders: {
                'Authorization': getToken()
            },
            // 上传附加数据
            uploadData: {},
            // 最大文件数量
            maxFiles: 10,
            // 最大文件大小(MB)
            maxSize: 50,
            // 上传状态
            uploading: false,
            uploadProgress: 0,
            progressStatus: null,
            progressText: '',
            // 选中的PDF类型
            selectedType: null,
            // PDF文件类型选项
            pdfTypes: [
                { value: 'manufacturer_qualification', label: '厂家资质', icon: 'el-icon-office-building', uploaded: false, filePath: null, fileID: null },
                { value: 'dealer_qualification', label: '经销商资质', icon: 'el-icon-user', uploaded: false, filePath: null, fileID: null },
                { value: 'customs_declaration', label: '报关单', icon: 'el-icon-document', uploaded: false, filePath: null, fileID: null },
                { value: 'inspection_report', label: '检验报告', icon: 'el-icon-data-analysis', uploaded: false, filePath: null, fileID: null },
                { value: 'cold_chain', label: '冷链', icon: 'el-icon-ice-cream', uploaded: false, filePath: null, fileID: null },
                { value: 'high_value_consumables', label: '高值耗材', icon: 'el-icon-star-on', uploaded: false, filePath: null, fileID: null },
                { value: 'goods_list', label: '随货清单', icon: 'el-icon-tickets', uploaded: false, filePath: null, fileID: null }
            ]
        }
    },

    methods: {
        /** 显示弹框 */
        show(type, deviceId, rowData = null) {
            this.operateType = type;
            this.currentDeviceId = deviceId;
            this.visible = true;
            this.resetUpload();
            // 设置上传附加数据
            this.uploadData = {
                deviceId: deviceId,
                operateType: type,
                name: `设备${deviceId}的PDF文件`
            };
            // 根据传入的行数据更新上传状态
            if (rowData) {
                this.updateUploadedStatusFromRow(rowData);
            }
        },

        /** 重置上传状态 */
        resetUpload() {
            this.fileList = [];
            this.uploading = false;
            this.uploadProgress = 0;
            this.progressStatus = null;
            this.progressText = '';
            this.selectedType = null;
        },

        /** 选择PDF类型 */
        selectType(typeValue) {
            this.selectedType = typeValue;
            this.fileList = [];
            this.$refs.pdfUpload && this.$refs.pdfUpload.clearFiles();
        },

        /** 获取当前选中类型的名称 */
        getCurrentTypeName() {
            const type = this.pdfTypes.find(t => t.value === this.selectedType);
            return type ? type.label : '';
        },

        /** 获取已上传的文件数量 */
        getUploadedCount() {
            return this.pdfTypes.filter(type => type.uploaded).length;
        },

        /** 根据行数据更新上传状态 */
        updateUploadedStatusFromRow(rowData) {
            // 重置所有状态
            this.pdfTypes.forEach(type => {
                type.uploaded = false;
                type.filePath = null;
                type.fileID = null;
            });

            if (rowData.fileDetails && typeof rowData.fileDetails === 'string') {
                this.parseFileInfoString(rowData.fileDetails);
            }

            // 更新标题显示文件总数
            const uploadedCount = this.getUploadedCount();
            if (uploadedCount > 0) {
                this.title = `PDF文件上传 (已上传${uploadedCount}个文件)`;
            } else {
                this.title = "PDF文件上传";
            }
        },

        /** 解析文件信息字符串 */
        parseFileInfoString(fileInfoString) {
            const fileInfos = fileInfoString.split('|').map(s => s.trim());
            fileInfos.forEach(fileInfo => {
                const fileIDMatch = fileInfo.match(/fileID:\s*([^-]+?)\s*-/);
                const filePathMatch = fileInfo.match(/filePath:\s*([^-]+?)\s*-/);
                const describeMatch = fileInfo.match(/describe:\s*(.+?)$/);

                if (fileIDMatch && filePathMatch && describeMatch) {
                    const fileID = fileIDMatch[1].trim();
                    const filePath = filePathMatch[1].trim();
                    const describe = describeMatch[1].trim();
                    const type = this.pdfTypes.find(t => t.value === describe);
                    if (type) {
                        type.uploaded = true;
                        type.filePath = filePath;
                        type.fileID = fileID;
                    }
                }
            });
        },

        /** 从响应中解析单个文件信息 */
        parseFileInfoFromResponse(fileInfoString, targetType) {
            // 解析最新上传的文件信息
            const fileInfos = fileInfoString.split('|').map(s => s.trim());
            // 查找匹配当前类型的最新文件信息
            for (let i = fileInfos.length - 1; i >= 0; i--) {
                const fileInfo = fileInfos[i];
                const fileIDMatch = fileInfo.match(/fileID:\s*([^-]+?)\s*-/);
                const filePathMatch = fileInfo.match(/filePath:\s*([^-]+?)\s*-/);
                const describeMatch = fileInfo.match(/describe:\s*(.+?)$/);

                if (fileIDMatch && filePathMatch && describeMatch) {
                    const describe = describeMatch[1].trim();
                    if (describe === targetType.value) {
                        targetType.filePath = filePathMatch[1].trim();
                        targetType.fileID = fileIDMatch[1].trim();
                        break;
                    }
                }
            }
        },

        /** 文件状态改变时的钩子 */
        handleFileChange(file, fileList) {
            // 验证文件类型
            const isPDF = file.raw.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
            if (!isPDF) {
                this.$modal.msgError('只能上传PDF格式的文件！');
                this.$refs.pdfUpload.handleRemove(file);
                return;
            }
            // 验证文件大小
            const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize;
            if (!isLtMaxSize) {
                this.$modal.msgError(`文件大小不能超过 ${this.maxSize}MB！`);
                this.$refs.pdfUpload.handleRemove(file);
                return;
            }
            // 更新本地文件列表
            this.fileList = fileList;
        },

        /** 上传成功 */
        handleSuccess(response, file) {
            this.uploading = false;
            this.uploadProgress = 100;
            this.progressStatus = 'success';
            this.progressText = `${file.name} 上传成功！`;

            if (response.code === 200 || response.code === '200') {
                this.$modal.msgSuccess(response.message || '文件上传成功！');
                this.$emit('refresh');
            } else {
                this.$modal.msgError(response.message || '文件上传失败！');
            }
        },

        /** 上传失败 */
        handleError() {
            this.uploading = false;
            this.uploadProgress = 0;
            this.progressStatus = 'exception';
            this.progressText = '上传失败！';

            this.$modal.msgError('文件上传失败，请重试！');
        },

        /** 移除文件 */
        handleRemove(file, fileList) {
            // 更新本地文件列表
            this.fileList = fileList;
            this.$modal.msgInfo(`已移除文件：${file.name}`);
        },

        /** 文件数量超出限制 */
        handleExceed(files, fileList) {
            this.$modal.msgWarning(`最多只能上传 ${this.maxFiles} 个文件，当前已选择 ${fileList.length} 个文件，本次选择了 ${files.length} 个文件`);
        },

        /** 确定上传 */
        async handleConfirm() {
            if (!this.selectedType) {
                this.$modal.msgWarning('请先选择PDF文件类型！');
                return;
            }

            if (!this.fileList || this.fileList.length === 0) {
                this.$modal.msgWarning('请先选择要上传的PDF文件！');
                return;
            }
            // 开始上传
            this.uploading = true;
            this.uploadProgress = 0;
            this.progressStatus = null;
            this.progressText = '正在上传文件...';
            try {
                // 参数验证
                if (!this.currentDeviceId) {
                    this.$modal.msgError('设备ID不能为空！');
                    this.uploading = false;
                    return;
                }
                // 创建FormData
                const formData = new FormData();
                formData.append('deviceId', String(this.currentDeviceId));
                formData.append('operateType', String(this.operateType || 'upload'));
                formData.append('name', `设备${this.currentDeviceId}的PDF文件`);
                formData.append('describe', String(this.selectedType));
                // 添加所有文件
                this.fileList.forEach(fileItem => {
                    if (fileItem.raw) {
                        formData.append('file', fileItem.raw);
                    }
                });

                // 验证关键参数
                if (!formData.get('deviceId')) {
                    this.$modal.msgError('FormData中设备ID为空！');
                    this.uploading = false;
                    return;
                }

                // 调用API上传
                const response = await EquipUploadPdf(formData);

                this.uploading = false;
                this.uploadProgress = 100;
                this.progressStatus = 'success';
                this.progressText = '上传完成！';

                if (response.code === 200 || response.code === '200') {
                    this.$modal.msgSuccess(response.message || '文件上传成功！');
                    const typeItem = this.pdfTypes.find(t => t.value === this.selectedType);
                    if (typeItem) {
                        typeItem.uploaded = true;
                        // 从响应中获取文件信息并更新本地状态
                        if (response.data) {
                            // 如果响应包含文件信息，直接使用
                            if (response.data.filePath) {
                                typeItem.filePath = response.data.filePath;
                            }
                            if (response.data.fileID) {
                                typeItem.fileID = response.data.fileID;
                            }
                            // 如果响应包含文件详情字符串，解析它
                            if (response.data.fileDetails && typeof response.data.fileDetails === 'string') {
                                this.parseFileInfoFromResponse(response.data.fileDetails, typeItem);
                            }
                        }
                    }

                    // 触发父组件刷新以获取最新数据
                    this.$emit('refresh');

                    // 延迟一小段时间后重新获取数据，确保UI状态同步
                    setTimeout(() => {
                        this.$emit('refresh-upload-status', this.currentDeviceId);
                    }, 500);

                    this.selectedType = null;
                    this.fileList = [];
                } else {
                    this.progressStatus = 'exception';
                    this.progressText = '上传失败！';
                    this.$modal.msgError(response.message || '文件上传失败！');
                }

            } catch (error) {
                this.uploading = false;
                this.uploadProgress = 0;
                this.progressStatus = 'exception';
                this.progressText = '上传失败！';
                this.$modal.msgError('文件上传失败，请重试！');
            }
        },

        /** 取消上传 */
        handleCancel() {
            this.visible = false;
            this.resetUpload();
        },

        /** 清空所有文件 */
        handleClearAll() {
            this.$modal.confirm('确定要清空所有已选择的文件吗？').then(() => {
                this.$refs.pdfUpload.clearFiles();
                this.fileList = [];
                this.$modal.msgSuccess('已清空所有文件');
            }).catch(() => { });
        },

        /** 查看文件 */
        viewFile(type) {
            if (!type.filePath) {
                this.$modal.msgWarning('文件路径不存在！');
                return;
            }

            // 构建完整的文件URL
            const fileUrl = process.env.VUE_APP_BASE_API + type.filePath;

            // 在新窗口中打开PDF文件
            window.open(fileUrl, '_blank');
        },

        /** 删除文件 */
        deleteFile(type) {
            if (!type.fileID) {
                this.$modal.msgWarning('文件ID不存在！');
                return;
            }

            const cleanFileID = type.fileID.toString().trim();

            this.$modal.confirm(`确定要删除${type.label}文件吗？`).then(async () => {
                try {
                    // 调用删除API
                    const response = await DeleteEquipPdf(cleanFileID);
                    if (response.code === 200 || response.code === '200') {
                        this.$modal.msgSuccess(response.message || '文件删除成功！');
                        // 更新本地状态
                        type.uploaded = false;
                        type.filePath = null;
                        type.fileID = null;
                        // 触发父组件刷新
                        this.$emit('refresh');
                    } else {
                        this.$modal.msgError(response.message || '文件删除失败！');
                    }
                } catch (error) {
                    this.$modal.msgError('文件删除失败，请重试！');
                }
            }).catch(() => { });
        },
    }
}
</script>

<style lang="scss" scoped>
.pdf-upload-dialog {
    ::v-deep .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 15px 20px;

        .el-dialog__title {
            color: #fff;
            font-weight: 600;
            font-size: 18px;
        }

        .el-dialog__close {
            color: #fff;
            font-size: 20px;

            &:hover {
                color: #f0f9ff;
            }
        }
    }

    ::v-deep .el-dialog__body {
        padding: 20px;
        background: #f8f9fa;
    }

    ::v-deep .el-dialog__footer {
        background: #fff;
        border-top: 1px solid #e4e7ed;
        padding: 15px 20px;
    }
}

.pdf-type-selector {
    margin-bottom: 20px;

    .selector-header {
        margin-bottom: 15px;

        h4 {
            margin: 0 0 5px 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
        }

        .selector-tip {
            margin: 0;
            color: #909399;
            font-size: 14px;

            .upload-summary {
                color: #667eea;
                font-weight: 500;
                margin-left: 8px;
            }
        }
    }

    .type-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;

        .type-item {
            padding: 12px 16px;
            border: 2px solid #e4e7ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;

            &:hover {
                border-color: #667eea;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
            }

            &.selected {
                border-color: #667eea;
                background: #f0f9ff;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
            }

            &.uploaded {
                border-color: #67c23a;
                background: #f0f9ff;

                &:hover {
                    border-color: #67c23a;
                }
            }

            .type-content {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .type-info {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex: 1; // 让label区域占据更多空间

                    i {
                        font-size: 18px;
                        color: #667eea;
                    }

                    .type-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #303133;
                    }
                }

                .type-status {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .el-tag {
                        font-size: 12px;
                    }
                }
            }

            // 操作按钮现在在单独的行
            .file-actions {
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #f0f0f0;
                display: flex;
                justify-content: center;
                gap: 8px;

                .action-btn {
                    padding: 4px 8px;
                    font-size: 12px;
                    border-radius: 4px;

                    i {
                        margin-right: 4px;
                    }

                    &.view-btn {
                        color: #667eea;
                        background: rgba(102, 126, 234, 0.1);

                        &:hover {
                            color: #409eff;
                            background: rgba(64, 158, 255, 0.1);
                        }
                    }

                    &.delete-btn {
                        color: #f56c6c;
                        background: rgba(245, 108, 108, 0.1);

                        &:hover {
                            color: #f78989;
                            background: rgba(247, 137, 137, 0.1);
                        }
                    }
                }
            }
        }
    }
}

.pdf-upload-wrapper {
    .upload-header {
        margin-bottom: 15px;

        h4 {
            margin: 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
        }
    }

    .pdf-upload {
        ::v-deep .el-upload-dragger {
            width: 100%;
            height: 200px;
            background: #fff;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            &:hover {
                border-color: #667eea;
                background: #f0f9ff;
            }

            .upload-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 15px;

                .upload-icon {
                    font-size: 48px;
                    color: #667eea;
                    margin-bottom: 10px;
                }

                .upload-text {
                    text-align: center;

                    .main-text {
                        font-size: 16px;
                        color: #606266;
                        margin: 0 0 8px 0;

                        em {
                            color: #667eea;
                            font-style: normal;
                            font-weight: 600;
                        }
                    }

                    .sub-text {
                        font-size: 14px;
                        color: #909399;
                        margin: 0;
                    }
                }
            }
        }

        ::v-deep .el-upload__tip {
            margin-top: 15px;

            .upload-tips {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 15px;
                background: #e6f7ff;
                border: 1px solid #91d5ff;
                border-radius: 6px;
                color: #1890ff;
                font-size: 14px;

                i {
                    font-size: 16px;
                }
            }
        }

        ::v-deep .el-upload-list {
            margin-top: 20px;

            .el-upload-list__item {
                background: #fff;
                border: 1px solid #e4e7ed;
                border-radius: 6px;
                padding: 10px 15px;
                margin-bottom: 10px;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #667eea;
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
                }

                .el-upload-list__item-name {
                    color: #606266;
                    font-weight: 500;
                }

                .el-icon-document {
                    color: #f56565;
                    margin-right: 8px;
                }

                .el-upload-list__item-status-label {
                    .el-icon-upload-success {
                        color: #67c23a;
                    }
                }
            }
        }
    }

    .upload-progress {
        margin-top: 20px;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        border: 1px solid #e4e7ed;

        .progress-text {
            margin-top: 10px;
            text-align: center;
            color: #606266;
            font-size: 14px;
        }
    }
}

.dialog-footer {
    text-align: right;

    .el-button {
        margin-left: 10px;

        &:first-child {
            margin-left: 0;
        }
    }
}
</style>
