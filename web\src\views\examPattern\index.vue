<template>
    <!-- 诊疗项目字典维护 -->
    <div class="inherit-platform">
        <div class="page-header">
            <div class="left">
                <div class="title">检查项目维护</div>
                <!-- <div class="subtitle">诊疗项目字典/名称维护</div> -->
            </div>
            <div class="right">
                <el-button type="primary" @click="openAddDialog = true">新增</el-button>
            </div>
        </div>
    
        <el-table v-loading="loading" :data="tableData" border stripe highlight-current-row style="width: 100%">
            <el-table-column type="index" label="序号" align="center" />
            <el-table-column label="项目类型" align="center" />
            <el-table-column label="项目代码" align="center" />
            <el-table-column label="项目名称" align="center" />
            <el-table-column label="操作" align="center" fixed="right">
                <template slot-scope="scope">
                    <el-button size="mini" type="primary" icon="el-icon-edit" plain>编辑</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <div class="empty-box">
                    <img :src="emptyImg" alt="暂无数据" class="empty-img" />
                    <div class="empty-text">暂无数据</div>
                </div>
            </template>
        </el-table>
    
        <!-- 新增项目 -->
        <el-dialog title="新增项目" :visible.sync="openAddDialog" width="900px" top="5vh" :modal-append-to-body="false"
            :close-on-click-modal="false" class="custom-add-dialog" @open="fetchDiagnosisDict">
            <!-- 上方表单 -->
            <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="90px" label-position="top"
                class="form-section">
                <div class="form-row">
                    <el-form-item label="项目类型" prop="ItemClass">
                        <el-select v-model="addForm.ItemClass" placeholder="请选择项目类型" @change="onFormChange">
                            <el-option v-for="item in diagnosisDict" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目代码" prop="ItemCode">
                        <el-input v-model="addForm.ItemCode" placeholder="请输入项目代码" @input="onFormChange" />
                    </el-form-item>
                    <el-form-item label="项目名称" prop="ItemName">
                        <el-input v-model="addForm.ItemName" placeholder="请输入项目名称" @input="onNameInput" />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="拼音码" prop="InputCode">
                        <el-input v-model="addForm.InputCode" placeholder="自动生成拼音码" disabled />
                    </el-form-item>
                </div>
            </el-form>
            <!-- 下方表格 -->
            <div class="table-section">
                <div class="table-header">
                    <span class="table-title">项目列表</span>
                    <span class="table-count">共 {{ filteredTableData.length }} 条</span>
                </div>
                <el-table :data="filteredTableData" border stripe highlight-current-row style="width: 100%" height="300">
                    <el-table-column type="index" label="序号" align="center" width="60" />
                    <el-table-column label="项目类型" align="center" prop="ItemClass" />
                    <el-table-column label="项目代码" align="center" prop="ItemCode" />
                    <el-table-column label="项目名称" align="center" prop="ItemName" />
                    <el-table-column label="拼音码" align="center" prop="InputCode" />
                    <el-table-column label="操作" align="center" fixed="right" width="120">
                        <template slot-scope="scope">
                            <el-button size="mini" type="primary" icon="el-icon-edit" plain
                                @click="handleEdit(scope.row)">编辑</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <div class="empty-box">
                            <img :src="emptyImg" alt="暂无数据" class="empty-img" />
                            <div class="empty-text">暂无数据</div>
                        </div>
                    </template>
                </el-table>
            </div>
            <!-- 新增项目按钮 -->
            <div slot="footer">
                <el-button @click="openAddDialog = false">取消</el-button>
                <el-button type="primary" @click="handleAdd">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
  GetDiagnosisDict, GetDiagnosisList, AddDiagnosis
  , GainSelectData, GainExamRptPatternData, RemoveExamRptPatternData
} from "@/api/examPattern/index"
import pinyin from 'pinyin'

export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      tableData: [],
      emptyImg: require('@/assets/images/1.png'),
      openAddDialog: false,
      diagnosisDict: [],
      addForm: {
        ItemClass: '',
        ItemCode: '',
        ItemName: '',
        InputCode: ''
      },
      addRules: {
        ItemClass: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
        ItemCode: [{ required: true, message: '请输入项目代码', trigger: 'blur' }],
        ItemName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
      }
    }
  },

  computed: {
    // 根据表单数据过滤的表格数据
    filteredTableData() {
      // 如果表单有数据，显示表单数据；否则显示原有表格数据
      if (this.addForm.ItemClass || this.addForm.ItemCode || this.addForm.ItemName) {
        // 返回表单数据作为表格数据
        return [{
          ItemClass: this.addForm.ItemClass,
          ItemCode: this.addForm.ItemCode,
          ItemName: this.addForm.ItemName,
          InputCode: this.addForm.InputCode
        }]
      } else {
        return this.tableData
      }
    }
  },

  created() {
    // this.getList()
  },

  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        this.tableData = res.data
      } catch (error) {
        this.$message.error('获取列表失败')
      }
      this.loading = false
    },

    // 获取诊疗项目字典数据
    fetchDiagnosisDict() {
      GetDiagnosisDict().then(res => {
        if (res && res.data) {
          this.diagnosisDict = res.data.map(item => ({
            label: item.clasS_NAME,
            value: item.clasS_CODE
          }))
        }
      })
    },

    // 项目名称输入
    onNameInput(val) {
      if (val) {
        // 取首字母大写拼音码
        const pyArr = pinyin(val, { style: pinyin.STYLE_FIRST_LETTER })
        this.addForm.InputCode = pyArr.map(arr => arr[0]).join('').toUpperCase()
      } else {
        this.addForm.InputCode = ''
      }
      // 触发表单变化事件
      this.onFormChange()
    },

    // 表单数据变化事件
    onFormChange() {
      // 表单数据变化时，表格会自动通过computed属性更新显示表单数据
      console.log('表单数据变化:', this.addForm)
    },

    // 编辑项目
    handleEdit(row) {
      // 将表格行数据填充到表单中
      this.addForm = {
        ItemClass: row.ItemClass,
        ItemCode: row.ItemCode,
        ItemName: row.ItemName,
        InputCode: row.InputCode
      }
    },

    // 新增项目
    handleAdd() {
      this.$refs.addFormRef.validate(valid => {
        if (!valid) return
        // 这里可以调用AddDiagnosis接口进行保存
        AddDiagnosis(this.addForm).then(res => {
          if (res && res.code === 200) {
            this.$message.success('新增成功')
            this.openAddDialog = false
            this.getList && this.getList()
          } else {
            this.$message.error(res.msg || '新增失败')
          }
        })
      })
    },
  }
}
</script>

<style lang="scss" scoped>
// 主容器样式
.inherit-platform {
    padding: 24px;
    background-color: #f6f8fa;
    min-height: calc(100vh - 84px);
}

// 页面头部样式
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 8px;

    .left {
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2329;
            margin-bottom: 8px;
        }

        .subtitle {
            font-size: 14px;
            color: #86909c;
        }
    }

    .right {
        .el-button {
            padding: 12px 24px;
            font-size: 14px;
            border-radius: 8px;
            background: #409eff;
            border: none;
            color: #fff;
            transition: all 0.3s;

            &:hover {
                background: #66b1ff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            }
        }
    }
}

// 空数据样式
.empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-img {
        width: 80px;
        height: 80px;
        margin-bottom: 12px;
        opacity: 0.7;
    }

    .empty-text {
        color: #999;
        font-size: 16px;
    }
}

.custom-add-dialog .el-dialog {
    border-radius: 14px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
}

.custom-add-dialog .el-dialog__header {
    background: #f5f7fa;
    border-radius: 14px 14px 0 0;
    padding: 18px 24px 10px 24px;
}

.custom-add-dialog .el-dialog__body {
    padding: 24px 32px 10px 32px;
    background: #fff;
}

.custom-add-dialog .el-form-item {
    margin-bottom: 22px;
}

.custom-add-dialog .el-input__inner {
    border-radius: 7px;
    height: 38px;
    font-size: 15px;
}

.custom-add-dialog .el-select .el-input__inner {
    border-radius: 7px;
}

.custom-add-dialog .el-dialog__footer {
    padding: 12px 32px 24px 32px;
    background: #f5f7fa;
    border-radius: 0 0 14px 14px;
    text-align: right;
}

.custom-add-dialog .el-button {
    min-width: 80px;
    border-radius: 7px;
    font-size: 15px;
    margin-left: 12px;
}

// 表单区域样式
.form-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 16px;
}

.form-row .el-form-item {
    flex: 1;
    margin-bottom: 0;
}

.form-section .el-form-item {
    margin-bottom: 16px;
}

.form-section .el-input__inner,
.form-section .el-select .el-input__inner {
    border-radius: 6px;
    height: 36px;
    font-size: 14px;
}

// 表格区域样式
.table-section {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2329;
}

.table-count {
    font-size: 14px;
    color: #86909c;
}

.table-section .el-table {
    border: none;
}

.table-section .el-table th {
    background: #f8f9fa;
    color: #1f2329;
    font-weight: 600;
}

.table-section .el-table td {
    padding: 8px 0;
}
</style>