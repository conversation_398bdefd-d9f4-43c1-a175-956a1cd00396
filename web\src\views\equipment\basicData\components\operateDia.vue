<template>
    <!-- 设别信息维护弹框页面 -->
    <el-dialog :title="title" :visible.sync="visible" width="90%" :close-on-click-modal="false"
        :close-on-press-escape="false" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">基本信息</div>
                <div class="form-grid">
                    <el-form-item label="条形码号" prop="barcode">
                        <el-input v-model="form.barcode" placeholder="请输入条形码号" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="院内编码" prop="hospitalCode">
                        <el-input v-model="form.hospitalCode" placeholder="请输入院内编码" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="27位医保码" prop="medicalInsuranceCode">
                        <el-input v-model="form.medicalInsuranceCode" placeholder="请输入27位医保码" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="物资分类" prop="materialCategory">
                        <el-select v-model="form.materialCategory" placeholder="请选择物资分类" :disabled="isDetail" filterable
                            allow-create default-first-option clearable>
                            <el-option v-for="dict in dict.type.material_category" :key="dict.value" :label="dict.label"
                                :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="医用耗材级别" prop="materialLevel">
                        <el-select v-model="form.materialLevel" placeholder="请选择医用耗材级别" :disabled="isDetail" filterable
                            allow-create default-first-option clearable>
                            <el-option v-for="dict in dict.type.material_level" :key="dict.value" :label="dict.label"
                                :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="风险等级" prop="riskLevel">
                        <el-select v-model="form.riskLevel" placeholder="请选择风险等级" :disabled="isDetail" filterable
                            allow-create default-first-option clearable>
                            <el-option v-for="dict in dict.type.risk_level" :key="dict.value" :label="dict.label"
                                :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="耗材名称" prop="materialName">
                        <el-input v-model="form.materialName" placeholder="请输入耗材名称" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="通用名" prop="genericName">
                        <el-input v-model="form.genericName" placeholder="请输入通用名" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="规格" prop="specification">
                        <el-input v-model="form.specification" placeholder="请输入规格" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="型号" prop="modelxh">
                        <el-input v-model="form.modelxh" placeholder="请输入" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="单位" prop="unit">
                        <el-input v-model="form.unit" placeholder="请输入单位" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="注册证号" prop="registrationNumber">
                        <el-input v-model="form.registrationNumber" placeholder="请输入注册证号" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="厂家" prop="manufacturer">
                        <el-input v-model="form.manufacturer" placeholder="请输入厂家" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="批号" prop="batchNumber">
                        <el-input v-model="form.batchNumber" placeholder="请输入批号" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="功能用途" prop="functionalUse">
                        <el-input v-model="form.functionalUse" placeholder="请输入功能用途" :disabled="isDetail" />
                    </el-form-item>
                </div>
            </div>

            <!-- 属性信息 -->
            <div class="form-section">
                <div class="section-title">属性信息</div>
                <div class="form-grid">
                    <el-form-item label="是否可收费" prop="isChargeable">
                        <el-radio-group v-model="form.isChargeable" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否采集" prop="isCollected">
                        <el-radio-group v-model="form.isCollected" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否高值" prop="isHighValue">
                        <el-radio-group v-model="form.isHighValue" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否18种特定" prop="isSpecific18">
                        <el-radio-group v-model="form.isSpecific18" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否植入性耗材" prop="isImplantable">
                        <el-radio-group v-model="form.isImplantable" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否一次性使用" prop="isDisposable">
                        <el-radio-group v-model="form.isDisposable" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否灭菌" prop="isSterile">
                        <el-radio-group v-model="form.isSterile" :disabled="isDetail">
                            <el-radio label="1">是</el-radio>
                            <el-radio label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="集采时间" prop="collectionTime">
                        <el-date-picker v-model="form.collectionTime" type="date" placeholder="选择集采时间"
                            :disabled="isDetail" />
                    </el-form-item>
                </div>
            </div>

            <!-- 包装和医保信息 -->
            <div class="form-section">
                <div class="section-title">包装和医保信息</div>
                <div class="form-grid">
                    <el-form-item label="包装" prop="packaging">
                        <el-input v-model="form.packaging" placeholder="请输入包装信息" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="医保类别" prop="medicalInsuranceCategory">
                        <el-select v-model="form.medicalInsuranceCategory" placeholder="请选择医保类别" :disabled="isDetail">
                            <el-option v-for="dict in dict.type.medical_insurance_category" :key="dict.value"
                                :label="dict.label" :value="dict.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="收费条码" prop="chargingBarcode">
                        <el-input v-model="form.chargingBarcode" placeholder="请输入收费条码" :disabled="isDetail" />
                    </el-form-item>
                </div>
            </div>

            <!-- 价格信息 -->
            <div class="form-section">
                <div class="section-title">价格信息</div>
                <div class="form-grid">
                    <el-form-item label="入库价格" prop="purchasePrice">
                        <el-input-number v-model="form.purchasePrice" :precision="2" :min="0" placeholder="请输入入库价格"
                            :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="售价" prop="salePrice">
                        <el-input-number v-model="form.salePrice" :precision="2" :min="0" placeholder="请输入售价"
                            :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="调价信息" prop="priceAdjustmentInfo">
                        <el-input v-model="form.priceAdjustmentInfo" placeholder="请输入调价信息" :disabled="isDetail" />
                    </el-form-item>
                </div>
            </div>

            <!-- 供应商和使用信息 -->
            <div class="form-section">
                <div class="section-title">供应商和使用信息</div>
                <div class="form-grid">
                    <el-form-item label="供应商" prop="supplier">
                        <el-input v-model="form.supplier" placeholder="请输入供应商" :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="适用科室" prop="applicableDepartment">
                        <el-input v-model="form.applicableDepartment" placeholder="请输入适用科室，多个用逗号分隔"
                            :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="有效期" prop="expiryDate">
                        <el-date-picker v-model="form.expiryDate" type="date" placeholder="选择有效期"
                            :disabled="isDetail" />
                    </el-form-item>
                    <el-form-item label="温湿度" prop="temperatureHumidity">
                        <el-input v-model="form.temperatureHumidity" placeholder="请输入温湿度要求" :disabled="isDetail" />
                    </el-form-item>
                </div>
            </div>
        </el-form>

        <div slot="footer" class="dialog-footer" v-if="!isDetail">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
        <div slot="footer" class="dialog-footer" v-else>
            <el-button @click="cancel">关 闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { addDevice, updateDevice, getDevice } from "@/api/equipment/device";

export default {
    name: "OperateDia",
    dicts: ['material_category', 'material_level', 'risk_level', 'yes_no', 'medical_insurance_category'],
    data() {
        return {
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            visible: false,
            // 是否为详情模式
            isDetail: false,
            // 操作类型
            operateType: '',
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                // barcode: [
                //     { required: true, message: "条形码号不能为空", trigger: "blur" }
                // ],
                // hospitalCode: [
                //     { required: true, message: "院内编码不能为空", trigger: "blur" }
                // ],
                // materialName: [
                //     { required: true, message: "耗材名称不能为空", trigger: "blur" }
                // ],
                // materialCategory: [
                //     { required: true, message: "物资分类不能为空", trigger: "change" }
                // ],
                // materialLevel: [
                //     { required: true, message: "医用耗材级别不能为空", trigger: "change" }
                // ],
                // riskLevel: [
                //     { required: true, message: "风险等级不能为空", trigger: "change" }
                // ],
                // manufacturer: [
                //     { required: true, message: "厂家不能为空", trigger: "blur" }
                // ],
                // unit: [
                //     { required: true, message: "单位不能为空", trigger: "blur" }
                // ]
            }
        }
    },
    methods: {
        /** 显示弹框 */
        show(type, id) {
            this.operateType = type
            this.isDetail = type === 'detail'
            this.visible = true
            this.reset()

            if (type === 'add') {
                this.title = "新增设备信息"
            } else if (type === 'edit') {
                this.title = "修改设备信息"
                this.getInfo(id)
            } else if (type === 'detail') {
                this.title = "设备信息详情"
                this.getInfo(id)
            }
        },
        /** 获取设备信息详情（回显） */
        getInfo(id) {
            const params = { id };
            getDevice(params).then(res => {
                const data = res.data || {};
                if (data.collectionTime) {
                    data.collectionTime = data.collectionTime ? new Date(data.collectionTime) : null;
                }
                if (data.expiryDate) {
                    data.expiryDate = data.expiryDate ? new Date(data.expiryDate) : null;
                }
                this.form = Object.assign({}, data);
            });
        },
        /** 表单重置 */
        reset() {
            this.form = {
                id: null,
                barcode: null,
                hospitalCode: null,
                medicalInsuranceCode: null,
                materialCategory: null,
                materialLevel: null,
                riskLevel: null,
                materialName: null,
                genericName: null,
                specification: null,
                modelxh: null,
                unit: null,
                registrationNumber: null,
                manufacturer: null,
                batchNumber: null,
                functionalUse: null,
                isChargeable: "0",
                isCollected: "0",
                collectionTime: null,
                isHighValue: "0",
                isSpecific18: "0",
                isImplantable: "0",
                isDisposable: "1",
                isSterile: "0",
                packaging: null,
                medicalInsuranceCategory: null,
                chargingBarcode: null,
                purchasePrice: null,
                salePrice: null,
                priceAdjustmentInfo: null,
                supplier: null,
                applicableDepartment: null,
                expiryDate: null,
                temperatureHumidity: null
            }
            this.resetForm("form")
        },
        /** 取消按钮 */
        cancel() {
            this.visible = false
            this.reset()
        },
        /** 表单提交 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.operateType === 'add') {
                        this.addDevice()
                    } else if (this.operateType === 'edit') {
                        this.updateDevice()
                    }
                }
            })
        },
        /** 新增设备信息 */
        addDevice() {
            addDevice(this.form).then(() => {
                this.$modal.msgSuccess("新增成功");
                this.visible = false;
                this.$emit('refresh');
            });
        },
        /** 修改设备信息 */
        updateDevice() {
            updateDevice(this.form).then(() => {
                this.$modal.msgSuccess("修改成功");
                this.visible = false;
                this.$emit('refresh');
            });
        }
    }
}
</script>

<style lang="scss" scoped>
// 弹框整体样式
::v-deep .el-dialog {
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

    .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 16px 24px;
        border-bottom: none;

        .el-dialog__title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .el-dialog__close {
            color: #fff;
            font-size: 18px;

            &:hover {
                color: #f0f9ff;
            }
        }
    }

    .el-dialog__body {
        padding: 20px 24px;
        max-height: 70vh;
        overflow-y: auto;
        background: #f8f9fa;

        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
    }

    .el-dialog__footer {
        background: #fff;
        padding: 16px 24px;
        border-top: 1px solid #e4e7ed;
        text-align: center;
    }
}

.form-section {
    background: #fff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    border: 1px solid #e4e7ed;

    .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;

        &::before {
            content: '';
            width: 3px;
            height: 14px;
            background: #667eea;
            border-radius: 2px;
            margin-right: 8px;
        }
    }
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;

    .el-form-item {
        margin-bottom: 0;
    }
}

// 表单项样式
::v-deep .el-form {
    .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
            font-weight: 500;
            color: #606266;
            font-size: 13px;
        }

        .el-input__inner,
        .el-select .el-input__inner,
        .el-date-editor .el-input__inner {
            border-radius: 4px;
            height: 32px;
            line-height: 32px;
            font-size: 13px;

            &:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
            }

            &:disabled {
                background-color: #f5f7fa;
                color: #909399;
            }
        }

        .el-input-number {
            width: 100%;

            .el-input__inner {
                height: 32px;
                line-height: 32px;
            }
        }

        .el-select,
        .el-date-editor {
            width: 100%;
        }
    }
}

// 单选按钮样式
::v-deep .el-radio-group {
    .el-radio {
        margin-right: 16px;
        margin-bottom: 6px;

        .el-radio__input {
            .el-radio__inner {
                width: 14px;
                height: 14px;

                &:hover {
                    border-color: #667eea;
                }

                &::after {
                    width: 4px;
                    height: 4px;
                    background: #667eea;
                }
            }

            &.is-checked .el-radio__inner {
                border-color: #667eea;
                background: #667eea;

                &::after {
                    background: #fff;
                }
            }
        }

        .el-radio__label {
            font-size: 13px;
            color: #606266;
            font-weight: 500;
            padding-left: 6px;
        }

        &.is-checked .el-radio__label {
            color: #667eea;
        }
    }
}

// 底部按钮样式
.dialog-footer {
    .el-button {
        border-radius: 4px;
        padding: 8px 20px;
        font-weight: 500;
        font-size: 13px;

        &.el-button--primary {
            background: #667eea;
            border-color: #667eea;

            &:hover {
                background: #5a6fd8;
                border-color: #5a6fd8;
            }
        }

        &.el-button--default {
            border: 1px solid #dcdfe6;
            color: #606266;

            &:hover {
                border-color: #667eea;
                color: #667eea;
            }
        }

        +.el-button {
            margin-left: 10px;
        }
    }
}

// 响应式
@media (max-width: 1200px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 14px;
    }
}

@media (max-width: 768px) {
    ::v-deep .el-dialog {
        width: 95% !important;
        margin: 0 auto;

        .el-dialog__body {
            padding: 16px;
        }
    }

    .form-section {
        padding: 12px;
        margin-bottom: 12px;

        .section-title {
            font-size: 13px;
            margin-bottom: 10px;
        }
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    ::v-deep .el-form {
        .el-form-item {
            margin-bottom: 12px;

            .el-form-item__label {
                font-size: 12px;
            }
        }
    }
}
</style>