<template>
  <div class="va-contrast-home">
    <el-drawer
      title="项目计价维护"
      :visible.sync="status"
      :direction="direction"
      size="60%">
      <div class="va-master">
        <div class="va-form">

        </div>
        <div class="va-item">

        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: 'valuationContrast',
  props: [],
  components: {},
  data() {
    return {
      status: false,
      direction: 'rtl'
    }
  },
  created() {
    this.init('010101000002FZ0')
  },
  mounted() {
  },
  methods: {
    init(data){
      console.log(data)
      this.status = true;
    },
  },
}
</script>

<style scoped lang="scss">
.va-contrast-home{
  ::v-deep.el-drawer__header {
    color: #ffffff !important;
    background-color: #185f7d;
    letter-spacing: 1em;
    font-size: 22px !important;
    text-align: center;
  }
  .va-master{
    .va-form{

    }
  }
}
</style>
