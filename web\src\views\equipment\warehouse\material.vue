<template>
    <!-- 设备管理列表页面 -->
    <div class="device-management-page">
        <!-- 页面标题栏 -->
        <div class="page-header">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="el-icon-monitor"></i>
                    设备管理列表
                </h1>
                <span class="page-subtitle">医疗设备信息的统一管理和查询</span>
            </div>
            <div class="header-right">
                <el-button type="text" @click="showSearch = !showSearch" class="toggle-search-btn">
                    <i :class="showSearch ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
                    {{ showSearch ? '收起筛选' : '展开筛选' }}
                </el-button>
            </div>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section" v-show="showSearch">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
                <el-form-item label="设备名称">
                    <el-input v-model="queryParams.deviceName" placeholder="设备名称" clearable size="mini"
                        style="width: 140px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="物权归属">
                    <el-select v-model="queryParams.ownership" placeholder="物权归属" clearable size="mini"
                        style="width: 120px">
                        <el-option label="医院" value="hospital" />
                        <el-option label="厂家" value="manufacturer" />
                        <el-option label="租赁" value="lease" />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备状态">
                    <el-select v-model="queryParams.status" placeholder="设备状态" clearable size="mini"
                        style="width: 120px">
                        <el-option label="正常" value="normal" />
                        <el-option label="维修" value="repair" />
                        <el-option label="停用" value="disabled" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="queryParams.onlyMyEquipment" label="只显示我的设备" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 按钮 -->
        <div class="toolbar-section">
            <div class="toolbar-left">
                <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                    新增设备
                </el-button>
                <el-button type="success" icon="el-icon-edit" :disabled="single" @click="handleUpdate">
                    主要信息修改
                </el-button>
                <el-button type="warning" icon="el-icon-document" :disabled="single" @click="handleBasicData">
                    基础数据录入
                </el-button>
            </div>
        </div>

        <!-- 表格 -->
        <div class="table-container">
            <div class="table-header">
                <div class="table-title">
                    <i class="el-icon-menu"></i>
                    <span>设备信息列表</span>
                </div>
            </div>
            <el-table ref="multipleTable" v-loading="loading" :data="equipmentList"
                @selection-change="handleSelectionChange" @row-click="handleRowClick" class="main-table" stripe border
                size="mini">
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="设备编码" align="center" prop="deviceCode" width="120" />
                <el-table-column label="设备名称" align="left" prop="deviceName" min-width="150" show-overflow-tooltip />
                <el-table-column label="设备型号" align="center" prop="deviceModel" width="120" show-overflow-tooltip />
                <el-table-column label="设备配置" align="center" prop="deviceConfig" width="100" show-overflow-tooltip />
                <el-table-column label="主要用途" align="center" prop="mainUsage" width="120" show-overflow-tooltip />
                <el-table-column label="生产厂家" align="center" prop="manufacturer" width="150" show-overflow-tooltip />
                <el-table-column label="操作" align="center" width="80" class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-wrapper">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="total" />
        </div>
    </div>
</template>

<script>
export default {
    name: "EquipmentList",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: false,
            // 总条数
            total: 0,
            // 设备表格数据
            equipmentList: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                deviceName: null,
                ownership: null,
                status: null,
                onlyMyEquipment: false
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询设备列表 */
        getList() {
            this.loading = true;
            // 模拟数据
            setTimeout(() => {
                let allData = [
                    {
                        id: 1,
                        deviceCode: "D20110001",
                        deviceName: "全自动生化分析仪",
                        deviceModel: "AU5800",
                        deviceConfig: "标准配置",
                        mainUsage: "生化检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 2,
                        deviceCode: "D20110002",
                        deviceName: "全自动血细胞分析仪",
                        deviceModel: "XN-3000",
                        deviceConfig: "标准配置",
                        mainUsage: "血常规检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 3,
                        deviceCode: "D20110003",
                        deviceName: "数字化X光机",
                        deviceModel: "DR-7500",
                        deviceConfig: "数字化",
                        mainUsage: "放射检查",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 4,
                        deviceCode: "D20110004",
                        deviceName: "彩超仪",
                        deviceModel: "LOGIQ E9",
                        deviceConfig: "彩色多普勒",
                        mainUsage: "超声检查",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 5,
                        deviceCode: "D20110005",
                        deviceName: "心电图机",
                        deviceModel: "MAC 5500",
                        deviceConfig: "12导联",
                        mainUsage: "心电检查",
                        manufacturer: "河南省医疗器械有限公司"
                    }
                ];

                // 应用搜索过滤
                let filteredData = allData;
                if (this.queryParams.deviceName) {
                    filteredData = filteredData.filter(item =>
                        item.deviceName.includes(this.queryParams.deviceName)
                    );
                }

                // 分页处理
                const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
                const end = start + this.queryParams.pageSize;
                this.equipmentList = filteredData.slice(start, end);
                this.total = filteredData.length;
                this.loading = false;
            }, 500);
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 行点击事件 */
        handleRowClick(row) {
            this.$refs.multipleTable.toggleRowSelection(row);
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$message.info("新增设备功能");
        },
        /** 修改按钮操作 */
        handleUpdate() {
            this.$message.info("主要信息修改功能");
        },
        /** 基础数据录入 */
        handleBasicData() {
            this.$message.info("基础数据录入功能");
        },
        /** 查看按钮操作 */
        handleView(row) {
            this.$message.info(`查看设备：${row.deviceName}`);
        },
        /** 分页大小改变 */
        handleSizeChange(val) {
            this.queryParams.pageSize = val;
            this.getList();
        },
        /** 当前页改变 */
        handleCurrentChange(val) {
            this.queryParams.pageNum = val;
            this.getList();
        }
    }
};
</script>

<style lang="scss" scoped>
.device-management-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 10px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .page-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;

            i {
                font-size: 28px;
            }
        }

        .page-subtitle {
            font-size: 15px;
            opacity: 0.9;
            margin-top: 2px;
        }

        .toggle-search-btn {
            color: #fff;
            font-size: 16px;
            padding: 0 4px;

            &:hover {
                color: #f0f9ff;
            }

            i {
                margin-right: 3px;
            }
        }
    }

    .search-section,
    .toolbar-section {
        background: #fff;
        border-bottom: 1px solid #e4e7ed;
        padding: 6px 16px;

        .search-form,
        .toolbar-left {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            ::v-deep .el-form-item {
                margin-right: 6px;
                margin-bottom: 2px;

                .el-form-item__label {
                    font-weight: 500;
                    color: #606266;
                    font-size: 15px;
                }

                .el-input__inner,
                .el-button {
                    height: 28px;
                    font-size: 15px;
                    border-radius: 4px;
                }

                .el-input__inner:focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
                }

                .el-button {
                    padding: 0 6px;
                }
            }
        }
    }

    .table-container {
        flex: 1;
        overflow-x: auto;
        overflow-y: auto;
        background: #fff;
        min-width: 600px;

        &::-webkit-scrollbar {
            height: 12px;
        }

        &::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;

            &:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        }

        .table-header {
            padding: 4px 10px;
            border-bottom: 1px solid #e4e7ed;

            .table-title {
                display: flex;
                align-items: center;
                gap: 4px;
                font-weight: 600;
                color: #303133;
                font-size: 18px;

                i {
                    color: #667eea;
                    font-size: 18px;
                }
            }
        }

        .main-table {

            ::v-deep .el-table__body-wrapper,
            ::v-deep .el-table__header-wrapper {
                overflow: visible !important;
            }

            ::v-deep .el-table {
                table-layout: auto !important;
            }

            ::v-deep .el-table__header th {
                background: #fafafa;
                font-weight: 600;
                font-size: 15px;
                height: 36px;
            }

            ::v-deep .el-table__body tr:hover {
                background: #f5f7fa;
            }

            ::v-deep .el-table__body td {
                font-size: 15px;
                height: 36px;
            }
        }
    }

    .pagination-wrapper {
        background: #fff;
        padding: 4px 10px;
        border-top: 1px solid #e4e7ed;
        text-align: center;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);

        ::v-deep .el-pagination {

            .el-pagination__total,
            .el-pagination__jump {
                font-size: 15px;
            }

            .el-pager li,
            .btn-prev,
            .btn-next {
                font-size: 15px;
                min-width: 28px;
                height: 28px;
                line-height: 28px;
            }
        }
    }
}

@media (max-width: 768px) {
    .device-management-page {
        .page-header .page-title {
            font-size: 20px;
        }

        .search-section,
        .toolbar-section {

            .search-form,
            .toolbar-left {
                ::v-deep .el-form-item {
                    .el-form-item__label {
                        font-size: 13px;
                    }

                    .el-input__inner,
                    .el-button {
                        height: 22px;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}
</style>
