<template>
    <!-- 仓库材料信息界面 -->
    <div class="device-management-page">
        <!-- 页面标题栏 -->
        <div class="page-header">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="el-icon-monitor"></i>
                    仓库材料定义
                </h1>
            </div>
            <div class="header-right">
                <el-button type="text" @click="showSearch = !showSearch" class="toggle-search-btn">
                    <i :class="showSearch ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
                    {{ showSearch ? '收起筛选' : '展开筛选' }}
                </el-button>
            </div>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section" v-show="showSearch">
            <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
                <el-form-item label="仓库">
                    <el-select v-model="queryParams.warehouse" placeholder="请选择仓库" clearable size="mini"
                        style="width: 140px">
                        <el-option label="中心仓库" value="center" />
                        <el-option label="药品仓库" value="medicine" />
                        <el-option label="器械仓库" value="equipment" />
                        <el-option label="耗材仓库" value="consumable" />
                    </el-select>
                </el-form-item>
                <el-form-item label="物资类别">
                    <el-select v-model="queryParams.materialCategory" placeholder="请选择物资类别" clearable size="mini"
                        style="width: 140px">
                        <el-option label="医疗器械" value="medical_equipment" />
                        <el-option label="医用耗材" value="medical_consumable" />
                        <el-option label="药品试剂" value="medicine_reagent" />
                        <el-option label="办公用品" value="office_supplies" />
                    </el-select>
                </el-form-item>
                <el-form-item label="物资材料名称">
                    <el-input v-model="queryParams.materialName" placeholder="物资材料名称" clearable size="mini"
                        style="width: 160px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="供应商">
                    <el-select v-model="queryParams.supplier" placeholder="请选择供应商" clearable size="mini"
                        style="width: 160px">
                        <el-option label="河南省医疗器械有限公司" value="henan_medical" />
                        <el-option label="北京医疗科技有限公司" value="beijing_medical" />
                        <el-option label="上海医疗设备有限公司" value="shanghai_medical" />
                        <el-option label="广州医疗器械有限公司" value="guangzhou_medical" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="queryParams.onlyAuthorizedMaterials" label="只显示拥有权限的材料" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 表格 -->
        <div class="table-container">
            <div class="table-header">
                <div class="table-title">
                    <i class="el-icon-menu"></i>
                    <span>材料信息列表</span>
                </div>
            </div>
            <el-table ref="multipleTable" v-loading="loading" :data="materialList" class="main-table" stripe border
                size="mini">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="材料代码" align="center" prop="materialCode" width="120" />
                <el-table-column label="材料名称" align="left" prop="materialName" min-width="200" show-overflow-tooltip />
                <el-table-column label="规格型号" align="center" prop="specification" width="150" show-overflow-tooltip />
                <el-table-column label="主要供应商" align="center" prop="mainSupplier" width="200" show-overflow-tooltip />
                <el-table-column label="是否有权" align="center" width="100">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.hasPermission" disabled></el-checkbox>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-wrapper">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="queryParams.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size="queryParams.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="total" />
        </div>
    </div>
</template>

<script>
export default {
    name: "MaterialList",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: false,
            // 总条数
            total: 0,
            // 材料表格数据
            materialList: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                warehouse: null,
                materialCategory: null,
                materialName: null,
                supplier: null,
                onlyAuthorizedMaterials: false
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询材料列表 */
        getList() {
            this.loading = true;
            // 模拟数据
            setTimeout(() => {
                let allData = [
                    {
                        id: 1,
                        materialCode: "M20110001",
                        materialName: "一次性注射器",
                        specification: "5ml",
                        mainSupplier: "河南省医疗器械有限公司",
                        hasPermission: true
                    },
                    {
                        id: 2,
                        materialCode: "M20110002",
                        materialName: "医用口罩",
                        specification: "N95",
                        mainSupplier: "北京医疗科技有限公司",
                        hasPermission: false
                    },
                    {
                        id: 3,
                        materialCode: "M20110003",
                        materialName: "输液器",
                        specification: "标准型",
                        mainSupplier: "上海医疗设备有限公司",
                        hasPermission: true
                    },
                    {
                        id: 4,
                        materialCode: "M20110004",
                        materialName: "医用手套",
                        specification: "乳胶无粉",
                        mainSupplier: "广州医疗器械有限公司",
                        hasPermission: true
                    },
                    {
                        id: 5,
                        materialCode: "M20110005",
                        materialName: "医用纱布",
                        specification: "10cm×10cm",
                        mainSupplier: "河南省医疗器械有限公司",
                        hasPermission: false
                    },
                    {
                        id: 6,
                        materialCode: "M20110006",
                        materialName: "血糖试纸",
                        specification: "50片/盒",
                        mainSupplier: "北京医疗科技有限公司",
                        hasPermission: true
                    },
                    {
                        id: 7,
                        materialCode: "M20110007",
                        materialName: "心电图纸",
                        specification: "210mm×30m",
                        mainSupplier: "上海医疗设备有限公司",
                        hasPermission: false
                    },
                    {
                        id: 8,
                        materialCode: "M20110008",
                        materialName: "医用酒精",
                        specification: "75% 500ml",
                        mainSupplier: "广州医疗器械有限公司",
                        hasPermission: true
                    }
                ];

                // 应用搜索过滤
                let filteredData = allData;
                if (this.queryParams.materialName) {
                    filteredData = filteredData.filter(item =>
                        item.materialName.includes(this.queryParams.materialName)
                    );
                }

                // 分页处理
                const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
                const end = start + this.queryParams.pageSize;
                this.materialList = filteredData.slice(start, end);
                this.total = filteredData.length;
                this.loading = false;
            }, 500);
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },

        /** 分页大小改变 */
        handleSizeChange(val) {
            this.queryParams.pageSize = val;
            this.getList();
        },
        /** 当前页改变 */
        handleCurrentChange(val) {
            this.queryParams.pageNum = val;
            this.getList();
        }
    }
};
</script>

<style lang="scss" scoped>
.device-management-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 10px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .page-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;

            i {
                font-size: 28px;
            }
        }

        .page-subtitle {
            font-size: 15px;
            opacity: 0.9;
            margin-top: 2px;
        }

        .toggle-search-btn {
            color: #fff;
            font-size: 16px;
            padding: 0 4px;

            &:hover {
                color: #f0f9ff;
            }

            i {
                margin-right: 3px;
            }
        }
    }

    .search-section,
    .toolbar-section {
        background: #fff;
        border-bottom: 1px solid #e4e7ed;
        padding: 6px 16px;

        .search-form,
        .toolbar-left {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;

            ::v-deep .el-form-item {
                margin-right: 6px;
                margin-bottom: 2px;

                .el-form-item__label {
                    font-weight: 500;
                    color: #606266;
                    font-size: 15px;
                }

                .el-input__inner,
                .el-button {
                    height: 28px;
                    font-size: 15px;
                    border-radius: 4px;
                }

                .el-input__inner:focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
                }

                .el-button {
                    padding: 0 6px;
                }
            }
        }
    }

    .table-container {
        flex: 1;
        overflow-x: auto;
        overflow-y: auto;
        background: #fff;
        min-width: 600px;

        &::-webkit-scrollbar {
            height: 12px;
        }

        &::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;

            &:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        }

        .table-header {
            padding: 4px 10px;
            border-bottom: 1px solid #e4e7ed;

            .table-title {
                display: flex;
                align-items: center;
                gap: 4px;
                font-weight: 600;
                color: #303133;
                font-size: 18px;

                i {
                    color: #667eea;
                    font-size: 18px;
                }
            }
        }

        .main-table {

            ::v-deep .el-table__body-wrapper,
            ::v-deep .el-table__header-wrapper {
                overflow: visible !important;
            }

            ::v-deep .el-table {
                table-layout: auto !important;
            }

            ::v-deep .el-table__header th {
                background: #fafafa;
                font-weight: 600;
                font-size: 15px;
                height: 36px;
            }

            ::v-deep .el-table__body tr:hover {
                background: #f5f7fa;
            }

            ::v-deep .el-table__body td {
                font-size: 15px;
                height: 36px;
            }
        }
    }

    .pagination-wrapper {
        background: #fff;
        padding: 4px 10px;
        border-top: 1px solid #e4e7ed;
        text-align: center;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);

        ::v-deep .el-pagination {

            .el-pagination__total,
            .el-pagination__jump {
                font-size: 15px;
            }

            .el-pager li,
            .btn-prev,
            .btn-next {
                font-size: 15px;
                min-width: 28px;
                height: 28px;
                line-height: 28px;
            }
        }
    }
}

@media (max-width: 768px) {
    .device-management-page {
        .page-header .page-title {
            font-size: 20px;
        }

        .search-section,
        .toolbar-section {

            .search-form,
            .toolbar-left {
                ::v-deep .el-form-item {
                    .el-form-item__label {
                        font-size: 13px;
                    }

                    .el-input__inner,
                    .el-button {
                        height: 22px;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}
</style>
